import fs from 'fs/promises';
import path from 'path';
import { logger } from '@/utils/logger';
import { CustomError } from '@/middleware/errorHandler';

export interface SpeechToTextResult {
  text: string;
  confidence: number;
  language: string;
  duration: number;
  words?: SpeechWord[];
  metadata: {
    processingTime: number;
    audioInfo: {
      format: string;
      duration: number;
      sampleRate?: number;
      channels?: number;
      size: number;
    };
  };
}

export interface SpeechWord {
  word: string;
  confidence: number;
  startTime: number;
  endTime: number;
}

class SpeechToTextService {
  private readonly supportedFormats = ['.wav', '.mp3', '.m4a', '.ogg', '.flac', '.webm'];
  private readonly maxFileSize = 25 * 1024 * 1024; // 25MB
  private readonly maxDuration = 300; // 5 minutes in seconds

  /**
   * Transcribe audio file to text
   */
  async transcribe(filePath: string, options?: {
    language?: string;
    includeWordTimestamps?: boolean;
    enhanceAudio?: boolean;
  }): Promise<string> {
    try {
      const startTime = Date.now();
      logger.info(`Starting speech-to-text processing for file: ${filePath}`);

      // Validate audio file
      await this.validateAudioFile(filePath);

      // Get audio metadata
      const audioInfo = await this.getAudioInfo(filePath);

      // Enhance audio if requested
      const processedFilePath = options?.enhanceAudio 
        ? await this.enhanceAudio(filePath)
        : filePath;

      // Perform transcription using Web Speech API fallback or external service
      const result = await this.performTranscription(processedFilePath, options);

      // Clean up processed file if it was created
      if (processedFilePath !== filePath) {
        await fs.unlink(processedFilePath).catch(() => {}); // Ignore errors
      }

      const processingTime = Date.now() - startTime;
      logger.info(`Speech-to-text completed in ${processingTime}ms`);

      return result.text;

    } catch (error) {
      logger.error('Error transcribing audio:', error);
      throw new CustomError('Failed to transcribe audio file');
    }
  }

  /**
   * Get detailed transcription result with timestamps
   */
  async transcribeDetailed(filePath: string, options?: {
    language?: string;
    includeWordTimestamps?: boolean;
    enhanceAudio?: boolean;
  }): Promise<SpeechToTextResult> {
    try {
      const startTime = Date.now();
      logger.info(`Starting detailed speech-to-text processing for file: ${filePath}`);

      // Validate audio file
      await this.validateAudioFile(filePath);

      // Get audio metadata
      const audioInfo = await this.getAudioInfo(filePath);

      // Enhance audio if requested
      const processedFilePath = options?.enhanceAudio 
        ? await this.enhanceAudio(filePath)
        : filePath;

      // Perform detailed transcription
      const transcriptionResult = await this.performDetailedTranscription(processedFilePath, options);

      // Clean up processed file if it was created
      if (processedFilePath !== filePath) {
        await fs.unlink(processedFilePath).catch(() => {}); // Ignore errors
      }

      const processingTime = Date.now() - startTime;

      const result: SpeechToTextResult = {
        text: transcriptionResult.text,
        confidence: transcriptionResult.confidence,
        language: transcriptionResult.language || options?.language || 'en-US',
        duration: audioInfo.duration,
        words: transcriptionResult.words,
        metadata: {
          processingTime,
          audioInfo
        }
      };

      logger.info(`Detailed speech-to-text completed in ${processingTime}ms`);
      return result;

    } catch (error) {
      logger.error('Error transcribing audio with details:', error);
      throw new CustomError('Failed to transcribe audio file with details');
    }
  }

  /**
   * Validate audio file
   */
  private async validateAudioFile(filePath: string): Promise<void> {
    try {
      // Check if file exists
      const stats = await fs.stat(filePath);
      
      // Check file size
      if (stats.size > this.maxFileSize) {
        throw new CustomError(`File size exceeds maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`);
      }

      // Check file extension
      const ext = path.extname(filePath).toLowerCase();
      if (!this.supportedFormats.includes(ext)) {
        throw new CustomError(`Unsupported audio format. Supported formats: ${this.supportedFormats.join(', ')}`);
      }

    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('Invalid audio file');
    }
  }

  /**
   * Get audio file information
   */
  private async getAudioInfo(filePath: string): Promise<any> {
    try {
      const stats = await fs.stat(filePath);
      const ext = path.extname(filePath).toLowerCase();

      // Basic audio info (in a real implementation, you'd use ffprobe or similar)
      return {
        format: ext.substring(1),
        duration: 0, // Would be extracted using audio analysis library
        sampleRate: 44100, // Default assumption
        channels: 2, // Default assumption
        size: stats.size
      };

    } catch (error) {
      logger.error('Error getting audio info:', error);
      return {
        format: 'unknown',
        duration: 0,
        sampleRate: 44100,
        channels: 2,
        size: 0
      };
    }
  }

  /**
   * Enhance audio quality for better transcription
   */
  private async enhanceAudio(filePath: string): Promise<string> {
    try {
      const enhancedPath = filePath.replace(/(\.[^.]+)$/, '_enhanced.wav');
      
      // In a real implementation, you would use ffmpeg or similar to:
      // - Normalize audio levels
      // - Reduce noise
      // - Convert to optimal format (WAV, 16kHz, mono)
      // - Apply audio filters
      
      // For now, just copy the file
      await fs.copyFile(filePath, enhancedPath);
      
      return enhancedPath;

    } catch (error) {
      logger.error('Error enhancing audio:', error);
      throw new CustomError('Failed to enhance audio');
    }
  }

  /**
   * Perform basic transcription
   */
  private async performTranscription(filePath: string, options?: {
    language?: string;
  }): Promise<{ text: string; confidence: number }> {
    try {
      // In a real implementation, this would integrate with:
      // - Google Speech-to-Text API
      // - Azure Speech Services
      // - AWS Transcribe
      // - OpenAI Whisper
      // - Local Whisper model
      
      // For demonstration, return a mock result
      const mockText = "This is a mock transcription result. In a real implementation, this would be the actual transcribed text from the audio file.";
      
      return {
        text: mockText,
        confidence: 0.85
      };

    } catch (error) {
      logger.error('Error performing transcription:', error);
      throw new CustomError('Transcription service failed');
    }
  }

  /**
   * Perform detailed transcription with word timestamps
   */
  private async performDetailedTranscription(filePath: string, options?: {
    language?: string;
    includeWordTimestamps?: boolean;
  }): Promise<{
    text: string;
    confidence: number;
    language?: string;
    words?: SpeechWord[];
  }> {
    try {
      // Mock detailed transcription result
      const words: SpeechWord[] = [
        { word: "This", confidence: 0.9, startTime: 0.0, endTime: 0.3 },
        { word: "is", confidence: 0.95, startTime: 0.3, endTime: 0.5 },
        { word: "a", confidence: 0.9, startTime: 0.5, endTime: 0.6 },
        { word: "mock", confidence: 0.85, startTime: 0.6, endTime: 0.9 },
        { word: "transcription", confidence: 0.8, startTime: 0.9, endTime: 1.5 },
        { word: "result", confidence: 0.9, startTime: 1.5, endTime: 1.9 }
      ];

      const text = words.map(w => w.word).join(' ');
      const avgConfidence = words.reduce((sum, w) => sum + w.confidence, 0) / words.length;

      return {
        text,
        confidence: avgConfidence,
        language: options?.language || 'en-US',
        words: options?.includeWordTimestamps ? words : undefined
      };

    } catch (error) {
      logger.error('Error performing detailed transcription:', error);
      throw new CustomError('Detailed transcription service failed');
    }
  }

  /**
   * Transcribe real-time audio stream (for future implementation)
   */
  async transcribeStream(audioStream: NodeJS.ReadableStream, options?: {
    language?: string;
    interimResults?: boolean;
  }): Promise<AsyncGenerator<{ text: string; isFinal: boolean }>> {
    // This would implement real-time streaming transcription
    throw new CustomError('Streaming transcription not yet implemented');
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): { code: string; name: string }[] {
    return [
      { code: 'en-US', name: 'English (US)' },
      { code: 'en-GB', name: 'English (UK)' },
      { code: 'es-ES', name: 'Spanish (Spain)' },
      { code: 'es-MX', name: 'Spanish (Mexico)' },
      { code: 'fr-FR', name: 'French (France)' },
      { code: 'de-DE', name: 'German (Germany)' },
      { code: 'it-IT', name: 'Italian (Italy)' },
      { code: 'pt-BR', name: 'Portuguese (Brazil)' },
      { code: 'ru-RU', name: 'Russian (Russia)' },
      { code: 'ja-JP', name: 'Japanese (Japan)' },
      { code: 'ko-KR', name: 'Korean (South Korea)' },
      { code: 'zh-CN', name: 'Chinese (Simplified)' },
      { code: 'zh-TW', name: 'Chinese (Traditional)' },
      { code: 'ar-SA', name: 'Arabic (Saudi Arabia)' },
      { code: 'hi-IN', name: 'Hindi (India)' },
      { code: 'th-TH', name: 'Thai (Thailand)' },
      { code: 'vi-VN', name: 'Vietnamese (Vietnam)' },
      { code: 'nl-NL', name: 'Dutch (Netherlands)' },
      { code: 'sv-SE', name: 'Swedish (Sweden)' },
      { code: 'no-NO', name: 'Norwegian (Norway)' }
    ];
  }

  /**
   * Detect language from audio
   */
  async detectLanguage(filePath: string): Promise<{
    language: string;
    confidence: number;
  }> {
    try {
      // In a real implementation, this would use language detection
      // For now, return a mock result
      return {
        language: 'en-US',
        confidence: 0.9
      };

    } catch (error) {
      logger.error('Error detecting language:', error);
      return {
        language: 'en-US',
        confidence: 0.5
      };
    }
  }

  /**
   * Convert audio format for better compatibility
   */
  async convertAudioFormat(
    inputPath: string, 
    outputFormat: 'wav' | 'mp3' | 'flac' = 'wav'
  ): Promise<string> {
    try {
      const outputPath = inputPath.replace(/\.[^.]+$/, `.${outputFormat}`);
      
      // In a real implementation, use ffmpeg to convert audio format
      // For now, just copy the file
      await fs.copyFile(inputPath, outputPath);
      
      return outputPath;

    } catch (error) {
      logger.error('Error converting audio format:', error);
      throw new CustomError('Failed to convert audio format');
    }
  }

  /**
   * Split long audio into chunks for processing
   */
  async splitAudio(filePath: string, chunkDuration: number = 60): Promise<string[]> {
    try {
      // In a real implementation, this would split audio into chunks
      // For now, return the original file
      return [filePath];

    } catch (error) {
      logger.error('Error splitting audio:', error);
      throw new CustomError('Failed to split audio file');
    }
  }
}

export const speechToTextService = new SpeechToTextService();
