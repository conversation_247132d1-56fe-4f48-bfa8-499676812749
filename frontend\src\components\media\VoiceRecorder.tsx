import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  LinearProgress,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  MicRounded,
  MicOffRounded,
  PlayArrowRounded,
  PauseRounded,
  StopRounded,
  DeleteRounded,
  SettingsRounded,
  VolumeUpRounded,
} from '@mui/icons-material';
import { formatDuration } from 'date-fns';

import { useVoiceRecording } from '@/hooks/useVoiceRecording';
import { useNotification } from '@/components/NotificationProvider';
import type { VoiceRecordingResult, VoiceRecordingConfig } from '@/types/media';

/**
 * Voice Recorder Component
 * TODO: Implement audio playback, waveform visualization, and upload functionality
 */

interface VoiceRecorderProps {
  onRecordingComplete?: (result: VoiceRecordingResult) => void;
  onError?: (error: string) => void;
  autoUpload?: boolean;
  className?: string;
}

export const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onError,
  autoUpload = false,
  className,
}) => {
  const { showSuccess, showError } = useNotification();
  
  // State
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const [recordingConfig, setRecordingConfig] = useState<Partial<VoiceRecordingConfig>>({
    quality: 'medium',
    maxDuration: 300,
    format: 'webm',
    noiseReduction: true,
    echoCancellation: true,
    autoGainControl: true,
  });

  // Voice recording hook
  const {
    recordingState,
    isSupported,
    permissions,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    cancelRecording,
    requestPermissions,
    getAudioDevices,
    setAudioDevice,
  } = useVoiceRecording({
    config: recordingConfig,
    onRecordingComplete: (result) => {
      showSuccess('Recording completed successfully!');
      onRecordingComplete?.(result);
    },
    onError: (error) => {
      showError(error);
      onError?.(error);
    },
    autoUpload,
  });

  // Load audio devices on mount
  useEffect(() => {
    const loadDevices = async () => {
      const devices = await getAudioDevices();
      setAudioDevices(devices);
      if (devices.length > 0 && !selectedDevice) {
        setSelectedDevice(devices[0].deviceId);
        setAudioDevice(devices[0].deviceId);
      }
    };
    
    if (permissions.granted) {
      loadDevices();
    }
  }, [permissions.granted, getAudioDevices, selectedDevice, setAudioDevice]);

  // Handle device change
  const handleDeviceChange = (deviceId: string) => {
    setSelectedDevice(deviceId);
    setAudioDevice(deviceId);
  };

  // Handle recording start
  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  // Handle recording stop
  const handleStopRecording = async () => {
    try {
      await stopRecording();
    } catch (error) {
      console.error('Failed to stop recording:', error);
    }
  };

  // Format duration display
  const formatRecordingDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get status color
  const getStatusColor = () => {
    switch (recordingState.status) {
      case 'recording': return 'error';
      case 'paused': return 'warning';
      case 'processing': return 'info';
      case 'completed': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  // Get audio level color
  const getAudioLevelColor = () => {
    if (recordingState.audioLevel > 80) return 'error';
    if (recordingState.audioLevel > 60) return 'warning';
    if (recordingState.audioLevel > 20) return 'success';
    return 'info';
  };

  if (!isSupported) {
    return (
      <Alert severity="error">
        Voice recording is not supported in this browser. Please use a modern browser with microphone support.
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Voice Recorder
          </Typography>
          <Box display="flex" gap={1}>
            <IconButton
              size="small"
              onClick={() => setSettingsOpen(true)}
              disabled={recordingState.status === 'recording'}
            >
              <SettingsRounded />
            </IconButton>
          </Box>
        </Box>

        {/* Permission Request */}
        {!permissions.granted && !permissions.denied && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2" gutterBottom>
              Microphone access is required for voice recording.
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={requestPermissions}
              disabled={permissions.requesting}
            >
              {permissions.requesting ? 'Requesting...' : 'Grant Permission'}
            </Button>
          </Alert>
        )}

        {/* Permission Denied */}
        {permissions.denied && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Microphone permission was denied. Please enable microphone access in your browser settings and refresh the page.
            </Typography>
          </Alert>
        )}

        {/* Recording Interface */}
        {permissions.granted && (
          <>
            {/* Status and Duration */}
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
              <Chip
                label={recordingState.status.replace('_', ' ').toUpperCase()}
                color={getStatusColor()}
                size="small"
              />
              <Typography variant="h4" fontWeight="mono">
                {formatRecordingDuration(recordingState.duration)}
              </Typography>
            </Box>

            {/* Audio Level Indicator */}
            {recordingState.status === 'recording' && (
              <Box mb={2}>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <VolumeUpRounded fontSize="small" />
                  <Typography variant="body2" color="text.secondary">
                    Audio Level: {recordingState.audioLevel}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={recordingState.audioLevel}
                  color={getAudioLevelColor()}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            )}

            {/* Duration Progress */}
            {recordingState.status === 'recording' && recordingConfig.maxDuration && (
              <Box mb={2}>
                <LinearProgress
                  variant="determinate"
                  value={(recordingState.duration / recordingConfig.maxDuration) * 100}
                  color="primary"
                  sx={{ height: 4, borderRadius: 2 }}
                />
                <Typography variant="caption" color="text.secondary" mt={0.5}>
                  Max duration: {formatRecordingDuration(recordingConfig.maxDuration)}
                </Typography>
              </Box>
            )}

            {/* Control Buttons */}
            <Box display="flex" justifyContent="center" gap={2} mb={2}>
              {recordingState.status === 'idle' && (
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<MicRounded />}
                  onClick={handleStartRecording}
                  color="primary"
                >
                  Start Recording
                </Button>
              )}

              {recordingState.status === 'recording' && (
                <>
                  <IconButton
                    size="large"
                    onClick={pauseRecording}
                    color="warning"
                  >
                    <PauseRounded />
                  </IconButton>
                  <IconButton
                    size="large"
                    onClick={handleStopRecording}
                    color="error"
                  >
                    <StopRounded />
                  </IconButton>
                </>
              )}

              {recordingState.status === 'paused' && (
                <>
                  <IconButton
                    size="large"
                    onClick={resumeRecording}
                    color="primary"
                  >
                    <PlayArrowRounded />
                  </IconButton>
                  <IconButton
                    size="large"
                    onClick={handleStopRecording}
                    color="error"
                  >
                    <StopRounded />
                  </IconButton>
                </>
              )}

              {(recordingState.status === 'recording' || recordingState.status === 'paused') && (
                <IconButton
                  size="large"
                  onClick={cancelRecording}
                  color="error"
                >
                  <DeleteRounded />
                </IconButton>
              )}
            </Box>

            {/* File Info */}
            {recordingState.fileSize > 0 && (
              <Box textAlign="center">
                <Typography variant="body2" color="text.secondary">
                  File size: {(recordingState.fileSize / 1024).toFixed(1)} KB
                </Typography>
              </Box>
            )}

            {/* Error Display */}
            {recordingState.error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {recordingState.error}
              </Alert>
            )}
          </>
        )}

        {/* Settings Dialog */}
        <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Recording Settings</DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" gap={3} pt={1}>
              {/* Audio Device Selection */}
              <FormControl fullWidth>
                <InputLabel>Microphone</InputLabel>
                <Select
                  value={selectedDevice}
                  onChange={(e) => handleDeviceChange(e.target.value)}
                  label="Microphone"
                >
                  {audioDevices.map((device) => (
                    <MenuItem key={device.deviceId} value={device.deviceId}>
                      {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Quality Setting */}
              <FormControl fullWidth>
                <InputLabel>Quality</InputLabel>
                <Select
                  value={recordingConfig.quality}
                  onChange={(e) => setRecordingConfig(prev => ({ ...prev, quality: e.target.value as any }))}
                  label="Quality"
                >
                  <MenuItem value="low">Low (16 kHz)</MenuItem>
                  <MenuItem value="medium">Medium (44.1 kHz)</MenuItem>
                  <MenuItem value="high">High (48 kHz)</MenuItem>
                </Select>
              </FormControl>

              {/* Max Duration */}
              <FormControl fullWidth>
                <InputLabel>Max Duration</InputLabel>
                <Select
                  value={recordingConfig.maxDuration}
                  onChange={(e) => setRecordingConfig(prev => ({ ...prev, maxDuration: e.target.value as number }))}
                  label="Max Duration"
                >
                  <MenuItem value={60}>1 minute</MenuItem>
                  <MenuItem value={300}>5 minutes</MenuItem>
                  <MenuItem value={600}>10 minutes</MenuItem>
                  <MenuItem value={1800}>30 minutes</MenuItem>
                </Select>
              </FormControl>

              {/* Audio Enhancement Options */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Audio Enhancement
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={recordingConfig.noiseReduction}
                      onChange={(e) => setRecordingConfig(prev => ({ ...prev, noiseReduction: e.target.checked }))}
                    />
                  }
                  label="Noise Reduction"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={recordingConfig.echoCancellation}
                      onChange={(e) => setRecordingConfig(prev => ({ ...prev, echoCancellation: e.target.checked }))}
                    />
                  }
                  label="Echo Cancellation"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={recordingConfig.autoGainControl}
                      onChange={(e) => setRecordingConfig(prev => ({ ...prev, autoGainControl: e.target.checked }))}
                    />
                  }
                  label="Auto Gain Control"
                />
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSettingsOpen(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};
