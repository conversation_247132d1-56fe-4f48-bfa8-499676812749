import Tesseract from 'tesseract.js';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '@/utils/logger';
import { CustomError } from '@/middleware/errorHandler';

export interface OCRResult {
  text: string;
  confidence: number;
  words: OCRWord[];
  blocks: OCRBlock[];
  metadata: {
    language: string;
    processingTime: number;
    imageInfo: {
      width: number;
      height: number;
      format: string;
      size: number;
    };
  };
}

export interface OCRWord {
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
}

export interface OCRBlock {
  text: string;
  confidence: number;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  words: OCRWord[];
}

class OCRService {
  private readonly supportedFormats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'];
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB
  private readonly maxDimension = 4000; // Max width or height in pixels

  /**
   * Extract text from image file
   */
  async extractText(filePath: string, options?: {
    language?: string;
    preprocessImage?: boolean;
    oem?: number;
    psm?: number;
  }): Promise<string> {
    try {
      const startTime = Date.now();
      logger.info(`Starting OCR processing for file: ${filePath}`);

      // Validate file
      await this.validateImageFile(filePath);

      // Preprocess image if needed
      const processedImagePath = options?.preprocessImage 
        ? await this.preprocessImage(filePath)
        : filePath;

      // Perform OCR
      const result = await this.performOCR(processedImagePath, options);

      // Clean up processed image if it was created
      if (processedImagePath !== filePath) {
        await fs.unlink(processedImagePath).catch(() => {}); // Ignore errors
      }

      const processingTime = Date.now() - startTime;
      logger.info(`OCR completed in ${processingTime}ms with confidence: ${result.confidence}`);

      return result.text;

    } catch (error) {
      logger.error('Error extracting text from image:', error);
      throw new CustomError('Failed to extract text from image');
    }
  }

  /**
   * Extract detailed OCR result with positioning information
   */
  async extractDetailedText(filePath: string, options?: {
    language?: string;
    preprocessImage?: boolean;
    oem?: number;
    psm?: number;
  }): Promise<OCRResult> {
    try {
      const startTime = Date.now();
      logger.info(`Starting detailed OCR processing for file: ${filePath}`);

      // Validate file
      await this.validateImageFile(filePath);

      // Get image metadata
      const imageInfo = await this.getImageInfo(filePath);

      // Preprocess image if needed
      const processedImagePath = options?.preprocessImage 
        ? await this.preprocessImage(filePath)
        : filePath;

      // Perform detailed OCR
      const ocrResult = await this.performDetailedOCR(processedImagePath, options);

      // Clean up processed image if it was created
      if (processedImagePath !== filePath) {
        await fs.unlink(processedImagePath).catch(() => {}); // Ignore errors
      }

      const processingTime = Date.now() - startTime;

      const result: OCRResult = {
        text: ocrResult.data.text,
        confidence: ocrResult.data.confidence,
        words: this.extractWords(ocrResult.data),
        blocks: this.extractBlocks(ocrResult.data),
        metadata: {
          language: options?.language || 'eng',
          processingTime,
          imageInfo
        }
      };

      logger.info(`Detailed OCR completed in ${processingTime}ms`);
      return result;

    } catch (error) {
      logger.error('Error extracting detailed text from image:', error);
      throw new CustomError('Failed to extract detailed text from image');
    }
  }

  /**
   * Validate image file
   */
  private async validateImageFile(filePath: string): Promise<void> {
    try {
      // Check if file exists
      const stats = await fs.stat(filePath);
      
      // Check file size
      if (stats.size > this.maxFileSize) {
        throw new CustomError(`File size exceeds maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`);
      }

      // Check file extension
      const ext = path.extname(filePath).toLowerCase();
      if (!this.supportedFormats.includes(ext)) {
        throw new CustomError(`Unsupported file format. Supported formats: ${this.supportedFormats.join(', ')}`);
      }

    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError('Invalid image file');
    }
  }

  /**
   * Get image information
   */
  private async getImageInfo(filePath: string): Promise<any> {
    try {
      const metadata = await sharp(filePath).metadata();
      const stats = await fs.stat(filePath);

      return {
        width: metadata.width || 0,
        height: metadata.height || 0,
        format: metadata.format || 'unknown',
        size: stats.size
      };

    } catch (error) {
      logger.error('Error getting image info:', error);
      return {
        width: 0,
        height: 0,
        format: 'unknown',
        size: 0
      };
    }
  }

  /**
   * Preprocess image for better OCR results
   */
  private async preprocessImage(filePath: string): Promise<string> {
    try {
      const processedPath = filePath.replace(/(\.[^.]+)$/, '_processed$1');
      
      await sharp(filePath)
        .resize(this.maxDimension, this.maxDimension, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .grayscale()
        .normalize()
        .sharpen()
        .toFile(processedPath);

      return processedPath;

    } catch (error) {
      logger.error('Error preprocessing image:', error);
      throw new CustomError('Failed to preprocess image');
    }
  }

  /**
   * Perform OCR using Tesseract
   */
  private async performOCR(filePath: string, options?: {
    language?: string;
    oem?: number;
    psm?: number;
  }): Promise<any> {
    try {
      const worker = await Tesseract.createWorker();
      
      await worker.loadLanguage(options?.language || 'eng');
      await worker.initialize(options?.language || 'eng');
      
      // Set OCR Engine Mode (OEM) and Page Segmentation Mode (PSM)
      if (options?.oem !== undefined) {
        await worker.setParameters({
          tessedit_ocr_engine_mode: options.oem
        });
      }
      
      if (options?.psm !== undefined) {
        await worker.setParameters({
          tessedit_pageseg_mode: options.psm
        });
      }

      const result = await worker.recognize(filePath);
      await worker.terminate();

      return result;

    } catch (error) {
      logger.error('Error performing OCR:', error);
      throw new CustomError('OCR processing failed');
    }
  }

  /**
   * Perform detailed OCR with positioning information
   */
  private async performDetailedOCR(filePath: string, options?: {
    language?: string;
    oem?: number;
    psm?: number;
  }): Promise<any> {
    try {
      const worker = await Tesseract.createWorker();
      
      await worker.loadLanguage(options?.language || 'eng');
      await worker.initialize(options?.language || 'eng');
      
      // Configure for detailed output
      await worker.setParameters({
        tessedit_ocr_engine_mode: options?.oem || 1, // LSTM OCR Engine
        tessedit_pageseg_mode: options?.psm || 3, // Fully automatic page segmentation
        preserve_interword_spaces: '1'
      });

      const result = await worker.recognize(filePath);
      await worker.terminate();

      return result;

    } catch (error) {
      logger.error('Error performing detailed OCR:', error);
      throw new CustomError('Detailed OCR processing failed');
    }
  }

  /**
   * Extract words with positioning from OCR result
   */
  private extractWords(ocrData: any): OCRWord[] {
    const words: OCRWord[] = [];

    if (ocrData.words) {
      ocrData.words.forEach((word: any) => {
        if (word.text.trim() && word.confidence > 30) { // Filter low confidence words
          words.push({
            text: word.text,
            confidence: word.confidence,
            bbox: {
              x0: word.bbox.x0,
              y0: word.bbox.y0,
              x1: word.bbox.x1,
              y1: word.bbox.y1
            }
          });
        }
      });
    }

    return words;
  }

  /**
   * Extract text blocks with positioning from OCR result
   */
  private extractBlocks(ocrData: any): OCRBlock[] {
    const blocks: OCRBlock[] = [];

    if (ocrData.blocks) {
      ocrData.blocks.forEach((block: any) => {
        if (block.text.trim() && block.confidence > 30) {
          const blockWords = this.extractWordsFromBlock(block);
          
          blocks.push({
            text: block.text,
            confidence: block.confidence,
            bbox: {
              x0: block.bbox.x0,
              y0: block.bbox.y0,
              x1: block.bbox.x1,
              y1: block.bbox.y1
            },
            words: blockWords
          });
        }
      });
    }

    return blocks;
  }

  /**
   * Extract words from a text block
   */
  private extractWordsFromBlock(block: any): OCRWord[] {
    const words: OCRWord[] = [];

    if (block.words) {
      block.words.forEach((word: any) => {
        if (word.text.trim() && word.confidence > 30) {
          words.push({
            text: word.text,
            confidence: word.confidence,
            bbox: {
              x0: word.bbox.x0,
              y0: word.bbox.y0,
              x1: word.bbox.x1,
              y1: word.bbox.y1
            }
          });
        }
      });
    }

    return words;
  }

  /**
   * Extract text from multiple images
   */
  async extractTextFromMultipleImages(filePaths: string[], options?: {
    language?: string;
    preprocessImage?: boolean;
    combineResults?: boolean;
  }): Promise<string[]> {
    try {
      const results = await Promise.all(
        filePaths.map(filePath => this.extractText(filePath, options))
      );

      if (options?.combineResults) {
        return [results.join('\n\n')];
      }

      return results;

    } catch (error) {
      logger.error('Error extracting text from multiple images:', error);
      throw new CustomError('Failed to extract text from multiple images');
    }
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return [
      'eng', 'spa', 'fra', 'deu', 'ita', 'por', 'rus', 'jpn', 'kor', 'chi_sim', 'chi_tra',
      'ara', 'hin', 'tha', 'vie', 'nld', 'swe', 'nor', 'dan', 'fin', 'pol', 'ces', 'hun'
    ];
  }

  /**
   * Detect text orientation
   */
  async detectOrientation(filePath: string): Promise<{
    angle: number;
    confidence: number;
  }> {
    try {
      const worker = await Tesseract.createWorker();
      await worker.loadLanguage('osd'); // Orientation and Script Detection
      await worker.initialize('osd');

      const result = await worker.detect(filePath);
      await worker.terminate();

      return {
        angle: result.data.orientation_degrees || 0,
        confidence: result.data.orientation_confidence || 0
      };

    } catch (error) {
      logger.error('Error detecting orientation:', error);
      return { angle: 0, confidence: 0 };
    }
  }

  /**
   * Auto-rotate image based on detected orientation
   */
  async autoRotateImage(filePath: string): Promise<string> {
    try {
      const orientation = await this.detectOrientation(filePath);
      
      if (orientation.angle === 0 || orientation.confidence < 0.5) {
        return filePath; // No rotation needed
      }

      const rotatedPath = filePath.replace(/(\.[^.]+)$/, '_rotated$1');
      
      await sharp(filePath)
        .rotate(-orientation.angle) // Negative angle to correct orientation
        .toFile(rotatedPath);

      return rotatedPath;

    } catch (error) {
      logger.error('Error auto-rotating image:', error);
      return filePath; // Return original if rotation fails
    }
  }
}

export const ocrService = new OCRService();
