{"name": "mindsync-frontend", "version": "1.0.0", "description": "MindSync Frontend - React application for productivity management", "private": true, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.8", "@mui/x-date-pickers": "^5.0.17", "@reduxjs/toolkit": "^1.9.2", "@types/d3": "^7.4.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.12", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "axios": "^1.3.4", "d3": "^7.8.2", "date-fns": "^2.29.3", "formik": "^2.2.9", "framer-motion": "^9.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.5", "react-redux": "^8.0.5", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-speech-recognition": "^3.10.0", "socket.io-client": "^4.6.1", "tesseract.js": "^4.0.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.0.2", "workbox-background-sync": "^6.5.4", "workbox-broadcast-update": "^6.5.4", "workbox-cacheable-response": "^6.5.4", "workbox-core": "^6.5.4", "workbox-expiration": "^6.5.4", "workbox-google-analytics": "^6.5.4", "workbox-navigation-preload": "^6.5.4", "workbox-precaching": "^6.5.4", "workbox-range-requests": "^6.5.4", "workbox-routing": "^6.5.4", "workbox-strategies": "^6.5.4", "workbox-streams": "^6.5.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/react-speech-recognition": "^3.9.0", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "cypress": "^12.6.0", "eslint": "^8.35.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4"}, "proxy": "http://localhost:5000", "homepage": "."}