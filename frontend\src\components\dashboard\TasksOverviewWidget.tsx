import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Box,
  Chip,
  Avatar,
  Checkbox,
  Divider,
} from '@mui/material';
import {
  AssignmentRounded,
  AddRounded,
  RadioButtonUncheckedRounded,
  CheckCircleRounded,
  PlayCircleRounded,
  PauseCircleRounded,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

import type { Task } from '@/store/api/tasksApi';
import { useUpdateTaskMutation } from '@/store/api/tasksApi';
import { useNotification } from '@/components/NotificationProvider';
import { InlineLoading } from '@/components/LoadingScreen';

interface TasksOverviewWidgetProps {
  tasks: Task[];
  loading: boolean;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircleRounded color="success" />;
    case 'in_progress':
      return <PlayCircleRounded color="primary" />;
    case 'cancelled':
      return <PauseCircleRounded color="error" />;
    default:
      return <RadioButtonUncheckedRounded color="action" />;
  }
};

const getPriorityColor = (priority: string): 'error' | 'warning' | 'info' | 'success' => {
  switch (priority) {
    case 'urgent':
      return 'error';
    case 'high':
      return 'warning';
    case 'medium':
      return 'info';
    case 'low':
      return 'success';
    default:
      return 'info';
  }
};

const truncateText = (text: string, maxLength: number = 60): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const TasksOverviewWidget: React.FC<TasksOverviewWidgetProps> = ({
  tasks,
  loading,
}) => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  const [updateTask] = useUpdateTaskMutation();

  const handleTaskToggle = async (task: Task) => {
    try {
      const newStatus = task.status === 'completed' ? 'todo' : 'completed';
      
      await updateTask({
        id: task.id,
        data: { status: newStatus },
      }).unwrap();

      showSuccess(
        newStatus === 'completed' 
          ? 'Task marked as completed!' 
          : 'Task marked as pending'
      );
    } catch (error) {
      showError('Failed to update task status');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} gutterBottom>
            Recent Tasks
          </Typography>
          <InlineLoading message="Loading tasks..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Recent Tasks
          </Typography>
          <Button
            size="small"
            endIcon={<AssignmentRounded />}
            onClick={() => navigate('/tasks')}
          >
            View All
          </Button>
        </Box>

        {tasks.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={4}
            textAlign="center"
          >
            <Avatar
              sx={{
                width: 64,
                height: 64,
                bgcolor: 'grey.100',
                color: 'grey.400',
                mb: 2,
              }}
            >
              <AssignmentRounded fontSize="large" />
            </Avatar>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              No tasks yet
            </Typography>
            <Typography variant="caption" color="text.secondary" mb={2}>
              Create your first task to get started!
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddRounded />}
              onClick={() => navigate('/tasks')}
            >
              Add Task
            </Button>
          </Box>
        ) : (
          <>
            <List disablePadding>
              {tasks.map((task, index) => (
                <React.Fragment key={task.id}>
                  <ListItem
                    disablePadding
                    sx={{
                      py: 1,
                      borderRadius: 1,
                      '&:hover': {
                        bgcolor: 'action.hover',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Checkbox
                        checked={task.status === 'completed'}
                        onChange={() => handleTaskToggle(task)}
                        size="small"
                        color="primary"
                      />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                          <Typography
                            variant="body2"
                            fontWeight={500}
                            sx={{
                              textDecoration: task.status === 'completed' ? 'line-through' : 'none',
                              opacity: task.status === 'completed' ? 0.7 : 1,
                            }}
                          >
                            {truncateText(task.title)}
                          </Typography>
                          <Chip
                            label={task.priority}
                            size="small"
                            color={getPriorityColor(task.priority)}
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.625rem' }}
                          />
                        </Box>
                      }
                      secondary={
                        <Box display="flex" alignItems="center" justifyContent="space-between">
                          <Typography variant="caption" color="text.secondary">
                            {formatDistanceToNow(new Date(task.createdAt), { addSuffix: true })}
                          </Typography>
                          {task.dueDate && (
                            <Typography
                              variant="caption"
                              color={
                                new Date(task.dueDate) < new Date() 
                                  ? 'error.main' 
                                  : 'text.secondary'
                              }
                            >
                              Due: {new Date(task.dueDate).toLocaleDateString()}
                            </Typography>
                          )}
                        </Box>
                      }
                      onClick={() => navigate(`/tasks?id=${task.id}`)}
                      sx={{ cursor: 'pointer' }}
                    />
                  </ListItem>
                  
                  {index < tasks.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>

            <Box mt={2} display="flex" gap={1}>
              <Button
                fullWidth
                variant="outlined"
                size="small"
                startIcon={<AddRounded />}
                onClick={() => navigate('/tasks')}
              >
                New Task
              </Button>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};
