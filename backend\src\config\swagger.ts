import { Options } from 'swagger-jsdoc';

export const swaggerOptions: Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'MindSync API',
      version: '1.0.0',
      description: 'Your second brain for productivity - A comprehensive API for managing tasks, goals, and knowledge',
      contact: {
        name: 'MindSync Support',
        email: '<EMAIL>',
        url: 'https://github.com/HectorTa1989/mindsync'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:5000',
        description: 'Development server'
      },
      {
        url: 'https://api.mindsync.app',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter JWT token'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique user identifier'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            firstName: {
              type: 'string',
              description: 'User first name'
            },
            lastName: {
              type: 'string',
              description: 'User last name'
            },
            role: {
              type: 'string',
              enum: ['user', 'admin', 'premium'],
              description: 'User role'
            },
            isVerified: {
              type: 'boolean',
              description: 'Whether user email is verified'
            },
            avatarUrl: {
              type: 'string',
              format: 'uri',
              description: 'User avatar URL'
            },
            timezone: {
              type: 'string',
              description: 'User timezone'
            },
            preferences: {
              type: 'object',
              description: 'User preferences'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation timestamp'
            },
            lastLoginAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last login timestamp'
            }
          }
        },
        Capture: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique capture identifier'
            },
            userId: {
              type: 'string',
              format: 'uuid',
              description: 'User who created the capture'
            },
            type: {
              type: 'string',
              enum: ['text', 'voice', 'image', 'email', 'web_clip'],
              description: 'Type of capture'
            },
            originalContent: {
              type: 'string',
              description: 'Original captured content'
            },
            processedContent: {
              type: 'string',
              description: 'Processed content after NLP/OCR/STT'
            },
            extractedEntities: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  type: { type: 'string' },
                  confidence: { type: 'number' }
                }
              },
              description: 'Extracted entities from content'
            },
            suggestedTags: {
              type: 'array',
              items: { type: 'string' },
              description: 'AI-suggested tags'
            },
            relatedNodes: {
              type: 'array',
              items: { type: 'object' },
              description: 'Related knowledge graph nodes'
            },
            confidence: {
              type: 'number',
              minimum: 0,
              maximum: 1,
              description: 'Processing confidence score'
            },
            metadata: {
              type: 'object',
              description: 'Additional metadata'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Capture creation timestamp'
            }
          }
        },
        Goal: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique goal identifier'
            },
            userId: {
              type: 'string',
              format: 'uuid',
              description: 'User who created the goal'
            },
            parentGoalId: {
              type: 'string',
              format: 'uuid',
              description: 'Parent goal ID for hierarchical structure'
            },
            title: {
              type: 'string',
              description: 'Goal title'
            },
            description: {
              type: 'string',
              description: 'Goal description'
            },
            status: {
              type: 'string',
              enum: ['active', 'completed', 'paused', 'cancelled'],
              description: 'Goal status'
            },
            targetValue: {
              type: 'number',
              description: 'Target value for measurable goals'
            },
            currentValue: {
              type: 'number',
              description: 'Current progress value'
            },
            unit: {
              type: 'string',
              description: 'Unit of measurement'
            },
            startDate: {
              type: 'string',
              format: 'date',
              description: 'Goal start date'
            },
            targetDate: {
              type: 'string',
              format: 'date',
              description: 'Goal target completion date'
            },
            priority: {
              type: 'integer',
              description: 'Goal priority (higher number = higher priority)'
            },
            color: {
              type: 'string',
              pattern: '^#[0-9A-Fa-f]{6}$',
              description: 'Goal color in hex format'
            },
            metadata: {
              type: 'object',
              description: 'Additional goal metadata'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Goal creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Goal last update timestamp'
            }
          }
        },
        Task: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique task identifier'
            },
            userId: {
              type: 'string',
              format: 'uuid',
              description: 'User who created the task'
            },
            goalId: {
              type: 'string',
              format: 'uuid',
              description: 'Associated goal ID'
            },
            title: {
              type: 'string',
              description: 'Task title'
            },
            description: {
              type: 'string',
              description: 'Task description'
            },
            status: {
              type: 'string',
              enum: ['todo', 'in_progress', 'completed', 'cancelled'],
              description: 'Task status'
            },
            priority: {
              type: 'string',
              enum: ['low', 'medium', 'high', 'urgent'],
              description: 'Task priority'
            },
            complexityScore: {
              type: 'integer',
              minimum: 1,
              maximum: 10,
              description: 'Task complexity score (1-10)'
            },
            estimatedDuration: {
              type: 'integer',
              description: 'Estimated duration in minutes'
            },
            actualDuration: {
              type: 'integer',
              description: 'Actual duration in minutes'
            },
            dueDate: {
              type: 'string',
              format: 'date-time',
              description: 'Task due date'
            },
            completedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Task completion timestamp'
            },
            tags: {
              type: 'array',
              items: { type: 'string' },
              description: 'Task tags'
            },
            metadata: {
              type: 'object',
              description: 'Additional task metadata'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Task creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Task last update timestamp'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  description: 'Error message'
                },
                code: {
                  type: 'string',
                  description: 'Error code'
                },
                statusCode: {
                  type: 'integer',
                  description: 'HTTP status code'
                },
                timestamp: {
                  type: 'string',
                  format: 'date-time',
                  description: 'Error timestamp'
                },
                path: {
                  type: 'string',
                  description: 'Request path'
                },
                method: {
                  type: 'string',
                  description: 'HTTP method'
                }
              }
            }
          }
        },
        TokenPair: {
          type: 'object',
          properties: {
            accessToken: {
              type: 'string',
              description: 'JWT access token'
            },
            refreshToken: {
              type: 'string',
              description: 'JWT refresh token'
            }
          }
        }
      }
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization'
      },
      {
        name: 'Users',
        description: 'User profile and account management'
      },
      {
        name: 'Capture',
        description: 'Universal capture hub for multi-modal input'
      },
      {
        name: 'Goals',
        description: 'Goal management and tracking'
      },
      {
        name: 'Tasks',
        description: 'Task management and productivity'
      },
      {
        name: 'Graph',
        description: 'Knowledge graph and relationships'
      },
      {
        name: 'Templates',
        description: 'Smart templates and patterns'
      },
      {
        name: 'Analytics',
        description: 'Analytics and insights'
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts'
  ]
};
