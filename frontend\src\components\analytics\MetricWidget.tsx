import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton,
  Tooltip,
  Chip,
  CircularProgress,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  MoreVertRounded,
  TrendingUpRounded,
  TrendingDownRounded,
  TrendingFlatRounded,
  EditRounded,
  DeleteRounded,
  RefreshRounded,
} from '@mui/icons-material';

import type { DashboardWidget } from '@/types/analytics';

/**
 * Metric Widget Component - Displays single metric values with trends
 * TODO: Implement real-time data fetching and advanced formatting options
 */

interface MetricWidgetProps {
  widget: DashboardWidget;
  editMode: boolean;
  onUpdate: (updates: Partial<DashboardWidget>) => void;
  onDelete: () => void;
}

interface MetricData {
  value: number;
  previousValue?: number;
  trend: 'up' | 'down' | 'flat';
  trendPercentage: number;
  unit: string;
  lastUpdated: Date;
}

export const MetricWidget: React.FC<MetricWidgetProps> = ({
  widget,
  editMode,
  onUpdate,
  onDelete,
}) => {
  // State
  const [metricData, setMetricData] = useState<MetricData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [configOpen, setConfigOpen] = useState(false);
  const [config, setConfig] = useState(widget.configuration.metric || {});

  // Load metric data on mount and when widget changes
  useEffect(() => {
    loadMetricData();
  }, [widget.configuration.metric?.metricId]);

  // Auto-refresh effect
  useEffect(() => {
    if (widget.refreshInterval <= 0) return;

    const interval = setInterval(() => {
      loadMetricData();
    }, widget.refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [widget.refreshInterval, widget.configuration.metric?.metricId]);

  // Load metric data
  const loadMetricData = async () => {
    if (!widget.configuration.metric?.metricId) return;

    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API call
      const mockData = await generateMockMetricData(widget.configuration.metric.metricId);
      setMetricData(mockData);

    } catch (error) {
      console.error('Error loading metric data:', error);
      setError('Failed to load metric data');
    } finally {
      setLoading(false);
    }
  };

  // Handle configuration save
  const handleConfigSave = () => {
    onUpdate({
      configuration: {
        ...widget.configuration,
        metric: config,
      },
    });
    setConfigOpen(false);
  };

  // Format metric value
  const formatValue = (value: number): string => {
    if (!config.format) return value.toString();

    switch (config.format) {
      case 'number':
        return value.toLocaleString();
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      case 'duration':
        const hours = Math.floor(value);
        const minutes = Math.round((value - hours) * 60);
        return `${hours}h ${minutes}m`;
      default:
        return value.toString();
    }
  };

  // Get trend icon
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpRounded color="success" />;
      case 'down':
        return <TrendingDownRounded color="error" />;
      case 'flat':
        return <TrendingFlatRounded color="action" />;
      default:
        return null;
    }
  };

  // Get trend color
  const getTrendColor = (trend: string, percentage: number) => {
    if (trend === 'flat') return 'text.secondary';
    
    const isPositive = trend === 'up';
    const isGoodTrend = config.higherIsBetter ? isPositive : !isPositive;
    
    return isGoodTrend ? 'success.main' : 'error.main';
  };

  // Get metric status color
  const getStatusColor = (value: number) => {
    if (!config.thresholds) return 'primary';
    
    if (value >= config.thresholds.good) return 'success';
    if (value >= config.thresholds.warning) return 'warning';
    return 'error';
  };

  return (
    <Card sx={{ height: '100%', position: 'relative' }}>
      <CardContent>
        {/* Widget Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600} noWrap>
            {widget.title}
          </Typography>
          
          {editMode && (
            <IconButton
              size="small"
              onClick={(e) => setMenuAnchor(e.currentTarget)}
            >
              <MoreVertRounded />
            </IconButton>
          )}
        </Box>

        {/* Loading State */}
        {loading && (
          <Box display="flex" justifyContent="center" alignItems="center" height={120}>
            <CircularProgress size={40} />
          </Box>
        )}

        {/* Error State */}
        {error && (
          <Box textAlign="center" py={4}>
            <Typography color="error" variant="body2">
              {error}
            </Typography>
            <Button
              size="small"
              startIcon={<RefreshRounded />}
              onClick={loadMetricData}
              sx={{ mt: 1 }}
            >
              Retry
            </Button>
          </Box>
        )}

        {/* Metric Display */}
        {metricData && !loading && !error && (
          <Box>
            {/* Main Value */}
            <Box textAlign="center" mb={2}>
              <Typography
                variant="h3"
                fontWeight={700}
                color={`${getStatusColor(metricData.value)}.main`}
              >
                {formatValue(metricData.value)}
              </Typography>
              {metricData.unit && (
                <Typography variant="body2" color="text.secondary">
                  {metricData.unit}
                </Typography>
              )}
            </Box>

            {/* Trend Information */}
            {config.showTrend && metricData.previousValue !== undefined && (
              <Box display="flex" alignItems="center" justifyContent="center" gap={1} mb={2}>
                {getTrendIcon(metricData.trend)}
                <Typography
                  variant="body2"
                  color={getTrendColor(metricData.trend, metricData.trendPercentage)}
                  fontWeight={500}
                >
                  {metricData.trendPercentage > 0 ? '+' : ''}
                  {metricData.trendPercentage.toFixed(1)}%
                </Typography>
              </Box>
            )}

            {/* Comparison */}
            {config.showComparison && metricData.previousValue !== undefined && (
              <Box textAlign="center" mb={2}>
                <Typography variant="caption" color="text.secondary">
                  vs {config.comparisonPeriod?.replace('_', ' ')}: {formatValue(metricData.previousValue)}
                </Typography>
              </Box>
            )}

            {/* Status Indicator */}
            {config.thresholds && (
              <Box display="flex" justifyContent="center">
                <Chip
                  label={getStatusLabel(metricData.value, config.thresholds)}
                  size="small"
                  color={getStatusColor(metricData.value)}
                  variant="outlined"
                />
              </Box>
            )}

            {/* Last Updated */}
            <Box textAlign="center" mt={2}>
              <Typography variant="caption" color="text.secondary">
                Updated {metricData.lastUpdated.toLocaleTimeString()}
              </Typography>
            </Box>
          </Box>
        )}

        {/* Empty State */}
        {!metricData && !loading && !error && (
          <Box textAlign="center" py={4}>
            <Typography color="text.secondary" variant="body2">
              No metric configured
            </Typography>
          </Box>
        )}
      </CardContent>

      {/* Widget Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          setConfigOpen(true);
          setMenuAnchor(null);
        }}>
          <EditRounded sx={{ mr: 1 }} />
          Configure
        </MenuItem>
        <MenuItem onClick={() => {
          loadMetricData();
          setMenuAnchor(null);
        }}>
          <RefreshRounded sx={{ mr: 1 }} />
          Refresh
        </MenuItem>
        <MenuItem onClick={() => {
          onDelete();
          setMenuAnchor(null);
        }}>
          <DeleteRounded sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Configuration Dialog */}
      <Dialog open={configOpen} onClose={() => setConfigOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Configure Metric Widget</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={3} pt={1}>
            <TextField
              label="Widget Title"
              value={widget.title}
              onChange={(e) => onUpdate({ title: e.target.value })}
              fullWidth
            />

            <FormControl fullWidth>
              <InputLabel>Metric</InputLabel>
              <Select
                value={config.metricId || ''}
                onChange={(e) => setConfig({ ...config, metricId: e.target.value })}
                label="Metric"
              >
                <MenuItem value="tasks_completed">Tasks Completed</MenuItem>
                <MenuItem value="completion_rate">Completion Rate</MenuItem>
                <MenuItem value="focus_time">Focus Time</MenuItem>
                <MenuItem value="productivity_score">Productivity Score</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Format</InputLabel>
              <Select
                value={config.format || 'number'}
                onChange={(e) => setConfig({ ...config, format: e.target.value })}
                label="Format"
              >
                <MenuItem value="number">Number</MenuItem>
                <MenuItem value="percentage">Percentage</MenuItem>
                <MenuItem value="currency">Currency</MenuItem>
                <MenuItem value="duration">Duration</MenuItem>
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={config.showTrend || false}
                  onChange={(e) => setConfig({ ...config, showTrend: e.target.checked })}
                />
              }
              label="Show trend indicator"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={config.showComparison || false}
                  onChange={(e) => setConfig({ ...config, showComparison: e.target.checked })}
                />
              }
              label="Show comparison to previous period"
            />

            {config.showComparison && (
              <FormControl fullWidth>
                <InputLabel>Comparison Period</InputLabel>
                <Select
                  value={config.comparisonPeriod || 'previous_period'}
                  onChange={(e) => setConfig({ ...config, comparisonPeriod: e.target.value })}
                  label="Comparison Period"
                >
                  <MenuItem value="previous_period">Previous Period</MenuItem>
                  <MenuItem value="previous_year">Previous Year</MenuItem>
                  <MenuItem value="target">Target Value</MenuItem>
                </Select>
              </FormControl>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfigOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleConfigSave}>
            Save Configuration
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};

// Helper function to get status label
function getStatusLabel(value: number, thresholds: any): string {
  if (value >= thresholds.good) return 'Good';
  if (value >= thresholds.warning) return 'Warning';
  return 'Critical';
}

// Mock metric data generation
async function generateMockMetricData(metricId: string): Promise<MetricData> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const baseValue = Math.random() * 100;
      const previousValue = baseValue + (Math.random() - 0.5) * 20;
      const trendPercentage = ((baseValue - previousValue) / previousValue) * 100;
      
      let trend: 'up' | 'down' | 'flat' = 'flat';
      if (Math.abs(trendPercentage) > 2) {
        trend = trendPercentage > 0 ? 'up' : 'down';
      }

      resolve({
        value: Math.round(baseValue),
        previousValue: Math.round(previousValue),
        trend,
        trendPercentage,
        unit: getMetricUnit(metricId),
        lastUpdated: new Date(),
      });
    }, 500);
  });
}

function getMetricUnit(metricId: string): string {
  switch (metricId) {
    case 'tasks_completed':
      return 'tasks';
    case 'completion_rate':
      return '%';
    case 'focus_time':
      return 'hours';
    case 'productivity_score':
      return 'points';
    default:
      return '';
  }
}
