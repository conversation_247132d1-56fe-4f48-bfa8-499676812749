import { renderHook, act } from '@testing-library/react';
import { useVoiceRecording } from '../useVoiceRecording';

/**
 * Unit tests for useVoiceRecording hook
 * TODO: Implement comprehensive tests for all recording scenarios
 */

// Mock MediaRecorder and related APIs
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  pause: jest.fn(),
  resume: jest.fn(),
  ondataavailable: null,
  onstop: null,
  state: 'inactive',
};

const mockMediaStream = {
  getTracks: jest.fn(() => [
    { stop: jest.fn(), kind: 'audio' }
  ]),
};

const mockAudioContext = {
  createAnalyser: jest.fn(() => ({
    fftSize: 256,
    frequencyBinCount: 128,
    getByteFrequencyData: jest.fn(),
    connect: jest.fn(),
  })),
  createMediaStreamSource: jest.fn(() => ({
    connect: jest.fn(),
  })),
  close: jest.fn(),
};

// Mock browser APIs
Object.defineProperty(global, 'MediaRecorder', {
  writable: true,
  value: jest.fn().mockImplementation(() => mockMediaRecorder),
});

Object.defineProperty(global.MediaRecorder, 'isTypeSupported', {
  writable: true,
  value: jest.fn().mockReturnValue(true),
});

Object.defineProperty(global, 'AudioContext', {
  writable: true,
  value: jest.fn().mockImplementation(() => mockAudioContext),
});

Object.defineProperty(global.navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockResolvedValue(mockMediaStream),
    enumerateDevices: jest.fn().mockResolvedValue([
      {
        deviceId: 'default',
        kind: 'audioinput',
        label: 'Default Microphone',
        groupId: 'group1',
      },
    ]),
  },
});

Object.defineProperty(global, 'crypto', {
  writable: true,
  value: {
    randomUUID: jest.fn().mockReturnValue('test-uuid-123'),
  },
});

describe('useVoiceRecording', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset MediaRecorder state
    mockMediaRecorder.state = 'inactive';
  });

  describe('initialization', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => useVoiceRecording());

      expect(result.current.recordingState.status).toBe('idle');
      expect(result.current.recordingState.duration).toBe(0);
      expect(result.current.recordingState.audioLevel).toBe(0);
      expect(result.current.recordingState.fileSize).toBe(0);
      expect(result.current.isSupported).toBe(true);
      expect(result.current.permissions.granted).toBe(false);
      expect(result.current.permissions.denied).toBe(false);
      expect(result.current.permissions.requesting).toBe(false);
    });

    it('should detect browser support correctly', () => {
      const { result } = renderHook(() => useVoiceRecording());
      expect(result.current.isSupported).toBe(true);
    });

    it('should handle unsupported browsers', () => {
      // Temporarily remove MediaRecorder
      const originalMediaRecorder = global.MediaRecorder;
      delete (global as any).MediaRecorder;

      const { result } = renderHook(() => useVoiceRecording());
      expect(result.current.isSupported).toBe(false);

      // Restore MediaRecorder
      global.MediaRecorder = originalMediaRecorder;
    });
  });

  describe('permissions', () => {
    it('should request permissions successfully', async () => {
      const { result } = renderHook(() => useVoiceRecording());

      await act(async () => {
        const granted = await result.current.requestPermissions();
        expect(granted).toBe(true);
      });

      expect(result.current.permissions.granted).toBe(true);
      expect(result.current.permissions.denied).toBe(false);
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
        audio: {
          deviceId: undefined,
          sampleRate: 44100,
          channelCount: 1,
          noiseSuppression: true,
          echoCancellation: true,
          autoGainControl: true,
        },
      });
    });

    it('should handle permission denial', async () => {
      const mockError = new Error('Permission denied');
      (navigator.mediaDevices.getUserMedia as jest.Mock).mockRejectedValueOnce(mockError);

      const { result } = renderHook(() => useVoiceRecording());

      await act(async () => {
        const granted = await result.current.requestPermissions();
        expect(granted).toBe(false);
      });

      expect(result.current.permissions.granted).toBe(false);
      expect(result.current.permissions.denied).toBe(true);
    });
  });

  describe('device management', () => {
    it('should get available audio devices', async () => {
      const { result } = renderHook(() => useVoiceRecording());

      await act(async () => {
        const devices = await result.current.getAudioDevices();
        expect(devices).toHaveLength(1);
        expect(devices[0].kind).toBe('audioinput');
      });

      expect(navigator.mediaDevices.enumerateDevices).toHaveBeenCalled();
    });

    it('should set audio device', () => {
      const { result } = renderHook(() => useVoiceRecording());

      act(() => {
        result.current.setAudioDevice('test-device-id');
      });

      // Device should be set for next recording session
      expect(result.current.setAudioDevice).toBeDefined();
    });
  });

  describe('recording lifecycle', () => {
    it('should start recording successfully', async () => {
      const { result } = renderHook(() => useVoiceRecording());

      // Grant permissions first
      await act(async () => {
        await result.current.requestPermissions();
      });

      await act(async () => {
        await result.current.startRecording();
      });

      expect(result.current.recordingState.status).toBe('recording');
      expect(mockMediaRecorder.start).toHaveBeenCalledWith(100);
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalled();
    });

    it('should stop recording and return result', async () => {
      const { result } = renderHook(() => useVoiceRecording());

      // Start recording first
      await act(async () => {
        await result.current.requestPermissions();
        await result.current.startRecording();
      });

      // Mock audio data
      const mockBlob = new Blob(['audio data'], { type: 'audio/webm' });
      global.Blob = jest.fn().mockImplementation(() => mockBlob);

      await act(async () => {
        const recordingResult = await result.current.stopRecording();
        expect(recordingResult).toBeTruthy();
        expect(recordingResult?.audioBlob).toBe(mockBlob);
        expect(recordingResult?.id).toBe('test-uuid-123');
      });

      expect(result.current.recordingState.status).toBe('completed');
      expect(mockMediaRecorder.stop).toHaveBeenCalled();
    });

    it('should pause and resume recording', () => {
      const { result } = renderHook(() => useVoiceRecording());

      // Set recording state
      act(() => {
        result.current.recordingState.status = 'recording';
      });

      act(() => {
        result.current.pauseRecording();
      });

      expect(mockMediaRecorder.pause).toHaveBeenCalled();

      // Set paused state
      act(() => {
        result.current.recordingState.status = 'paused';
      });

      act(() => {
        result.current.resumeRecording();
      });

      expect(mockMediaRecorder.resume).toHaveBeenCalled();
    });

    it('should cancel recording', () => {
      const { result } = renderHook(() => useVoiceRecording());

      act(() => {
        result.current.cancelRecording();
      });

      expect(result.current.recordingState.status).toBe('idle');
      expect(result.current.recordingState.duration).toBe(0);
      expect(result.current.recordingState.audioLevel).toBe(0);
      expect(result.current.recordingState.fileSize).toBe(0);
    });
  });

  describe('configuration', () => {
    it('should use custom configuration', () => {
      const customConfig = {
        quality: 'high' as const,
        maxDuration: 600,
        format: 'wav' as const,
        sampleRate: 48000 as const,
        channels: 2 as const,
        noiseReduction: false,
        echoCancellation: false,
        autoGainControl: false,
      };

      const { result } = renderHook(() => useVoiceRecording({ config: customConfig }));

      // Configuration should be applied when starting recording
      expect(result.current.startRecording).toBeDefined();
    });

    it('should call onRecordingComplete callback', async () => {
      const onRecordingComplete = jest.fn();
      const { result } = renderHook(() => useVoiceRecording({ onRecordingComplete }));

      // Mock successful recording
      await act(async () => {
        await result.current.requestPermissions();
        await result.current.startRecording();
      });

      const mockBlob = new Blob(['audio data'], { type: 'audio/webm' });
      global.Blob = jest.fn().mockImplementation(() => mockBlob);

      await act(async () => {
        await result.current.stopRecording();
      });

      expect(onRecordingComplete).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'test-uuid-123',
          audioBlob: mockBlob,
          processingStatus: 'pending',
        })
      );
    });

    it('should call onError callback', async () => {
      const onError = jest.fn();
      const { result } = renderHook(() => useVoiceRecording({ onError }));

      // Mock getUserMedia failure
      const mockError = new Error('Microphone not available');
      (navigator.mediaDevices.getUserMedia as jest.Mock).mockRejectedValueOnce(mockError);

      await act(async () => {
        await result.current.requestPermissions();
      });

      expect(onError).toHaveBeenCalledWith('Microphone permission denied');
    });
  });

  describe('error handling', () => {
    it('should handle MediaRecorder creation failure', async () => {
      const onError = jest.fn();
      const { result } = renderHook(() => useVoiceRecording({ onError }));

      // Mock MediaRecorder constructor failure
      (global.MediaRecorder as jest.Mock).mockImplementationOnce(() => {
        throw new Error('MediaRecorder not supported');
      });

      await act(async () => {
        await result.current.requestPermissions();
        await result.current.startRecording();
      });

      expect(result.current.recordingState.status).toBe('error');
      expect(onError).toHaveBeenCalled();
    });

    it('should handle getUserMedia failure during recording', async () => {
      const onError = jest.fn();
      const { result } = renderHook(() => useVoiceRecording({ onError }));

      // Grant permissions first
      await act(async () => {
        await result.current.requestPermissions();
      });

      // Mock getUserMedia failure on recording start
      (navigator.mediaDevices.getUserMedia as jest.Mock).mockRejectedValueOnce(
        new Error('Device busy')
      );

      await act(async () => {
        await result.current.startRecording();
      });

      expect(onError).toHaveBeenCalledWith('Failed to start recording');
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources on unmount', () => {
      const { unmount } = renderHook(() => useVoiceRecording());

      unmount();

      // Should not throw any errors during cleanup
      expect(true).toBe(true);
    });

    it('should stop media stream on cancel', () => {
      const { result } = renderHook(() => useVoiceRecording());

      act(() => {
        result.current.cancelRecording();
      });

      // Media stream tracks should be stopped
      expect(mockMediaStream.getTracks()[0].stop).toHaveBeenCalled();
    });
  });
});
