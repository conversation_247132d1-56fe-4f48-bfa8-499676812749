import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';

export const SettingsPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Settings' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Settings
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Configure your application preferences and settings.
      </Typography>

      <Alert severity="info">
        <Typography variant="body2">
          Settings interface is coming soon! This will include theme preferences, notification settings, and application configuration.
        </Typography>
      </Alert>
    </Box>
  );
};
