# Voice and Image Capture Features

## Overview

The Voice and Image Capture features provide foundational infrastructure for recording audio, capturing images, and processing media content within the MindSync application. This document outlines the implemented interfaces, components, and planned functionality.

## Architecture

### Frontend Components

#### 1. TypeScript Interfaces (`frontend/src/types/media.ts`)

**Voice Recording Types:**
- `VoiceRecordingConfig` - Configuration for audio recording settings
- `VoiceRecordingState` - Current state of recording session
- `VoiceRecordingResult` - Complete recording result with metadata
- `TranscriptionResult` - Speech-to-text processing results

**Image Capture Types:**
- `ImageCaptureConfig` - Configuration for image capture settings
- `ImageCaptureResult` - Complete image capture result with metadata
- `OCRResult` - Optical character recognition results

**Media Storage Types:**
- `MediaStorageConfig` - Storage provider configuration
- `MediaProcessingJob` - Background processing job tracking
- `MediaPreferences` - User preferences for media handling

#### 2. Custom Hooks

**`useVoiceRecording` Hook (`frontend/src/hooks/useVoiceRecording.ts`)**
```typescript
const {
  recordingState,
  isSupported,
  permissions,
  startRecording,
  stopRecording,
  pauseRecording,
  resumeRecording,
  cancelRecording,
  requestPermissions,
  getAudioDevices,
  setAudioDevice,
} = useVoiceRecording({
  config: recordingConfig,
  onRecordingComplete: handleComplete,
  onError: handleError,
  autoUpload: true,
});
```

**Features:**
- Real-time audio level monitoring
- Multiple audio format support (WebM, MP4, WAV, OGG)
- Configurable quality settings
- Device selection and management
- Permission handling
- Automatic duration limits
- Audio enhancement options (noise reduction, echo cancellation)

**`useImageCapture` Hook (`frontend/src/hooks/useImageCapture.ts`)**
```typescript
const {
  isSupported,
  isCameraActive,
  permissions,
  startCamera,
  stopCamera,
  captureImage,
  uploadFromFile,
  requestPermissions,
  getCameraDevices,
  setCameraDevice,
  videoRef,
} = useImageCapture({
  config: captureConfig,
  onImageCaptured: handleCapture,
  onError: handleError,
  autoUpload: true,
});
```

**Features:**
- Camera preview with live video feed
- Front/back camera switching
- Image quality and compression settings
- File upload from device storage
- Automatic image resizing
- Device selection and management

#### 3. React Components

**`VoiceRecorder` Component (`frontend/src/components/media/VoiceRecorder.tsx`)**
- Complete recording interface with visual feedback
- Audio level visualization
- Recording duration display and progress
- Device selection settings
- Quality configuration options
- Recording controls (start, stop, pause, resume, cancel)

**`ImageCapture` Component (`frontend/src/components/media/ImageCapture.tsx`)**
- Camera preview with live video feed
- Capture controls and settings
- Image preview dialog
- File upload interface
- Quality and compression settings
- Device management

### Backend Services

#### 1. Media Service (`backend/src/services/mediaService.ts`)

**Core Functionality:**
- File upload and storage management
- Media metadata extraction and storage
- Processing queue management
- File validation and security checks

**Key Methods:**
```typescript
// Upload media file
await mediaService.uploadMedia({
  userId,
  type: 'voice' | 'image',
  file: { buffer, originalName, mimeType },
  metadata: {},
  processingOptions: { autoTranscribe, autoOCR, language }
});

// Process voice recording
await mediaService.processVoiceRecording(mediaId, {
  language: 'en',
  includeTimestamps: true,
  enhanceAudio: true
});

// Process image OCR
await mediaService.processImageOCR(mediaId, {
  language: 'en',
  includePositions: true,
  preprocessImage: true
});
```

#### 2. API Routes (`backend/src/routes/media.ts`)

**Voice Endpoints:**
- `POST /api/media/voice/upload` - Upload voice recording
- `POST /api/media/voice/:id/transcribe` - Transcribe audio to text

**Image Endpoints:**
- `POST /api/media/image/upload` - Upload image file
- `POST /api/media/image/:id/ocr` - Extract text from image

**General Endpoints:**
- `GET /api/media/:id` - Get media file details
- `DELETE /api/media/:id` - Delete media file
- `GET /api/media` - List user's media files

## Configuration Options

### Voice Recording Configuration

```typescript
interface VoiceRecordingConfig {
  quality: 'low' | 'medium' | 'high' | 'lossless';
  maxDuration: number; // seconds
  format: 'webm' | 'mp4' | 'wav' | 'ogg';
  sampleRate: 16000 | 22050 | 44100 | 48000;
  channels: 1 | 2;
  noiseReduction: boolean;
  echoCancellation: boolean;
  autoGainControl: boolean;
}
```

### Image Capture Configuration

```typescript
interface ImageCaptureConfig {
  quality: number; // 0-1
  maxWidth: number;
  maxHeight: number;
  format: 'jpeg' | 'png' | 'webp';
  enableCompression: boolean;
  compressionQuality: number; // 0-1
  preserveExif: boolean;
}
```

## Integration Points

### 1. Existing Capture System
The media capture features integrate with the existing capture system in:
- `backend/src/services/captureService.ts` - Processes voice and image captures
- `frontend/src/components/dashboard/QuickCaptureWidget.tsx` - UI integration point

### 2. Storage Integration
- Local file storage with configurable upload paths
- Metadata storage in existing database schema
- Integration with existing user authentication and authorization

### 3. Processing Pipeline
- Queue-based processing for transcription and OCR
- Integration with existing NLP service for text analysis
- Metadata extraction and storage

## Testing

### Unit Tests
- `frontend/src/hooks/__tests__/useVoiceRecording.test.ts` - Comprehensive hook testing
- Mock implementations for browser APIs (MediaRecorder, getUserMedia)
- Test coverage for all recording scenarios and error conditions

### Test Scenarios Covered
- Browser support detection
- Permission handling (grant/deny)
- Device enumeration and selection
- Recording lifecycle (start/stop/pause/resume/cancel)
- Configuration options
- Error handling and recovery
- Resource cleanup

## TODO: Future Implementation

### High Priority
1. **Actual Media Processing Integration**
   - Integrate with speech-to-text services (Whisper, Google Speech, Azure)
   - Implement OCR processing (Tesseract, Google Vision, Azure)
   - Add audio enhancement and image preprocessing

2. **Cloud Storage Integration**
   - AWS S3, Google Cloud Storage, or Azure Blob Storage
   - Secure upload URLs and signed access
   - Automatic backup and synchronization

3. **Database Schema Implementation**
   - Media files table with proper indexing
   - Processing results storage
   - User preferences and settings

### Medium Priority
1. **Advanced Audio Features**
   - Waveform visualization
   - Audio playback controls
   - Noise reduction and enhancement
   - Multiple microphone support

2. **Advanced Image Features**
   - Image editing tools (crop, rotate, filters)
   - Batch image processing
   - Image annotation and markup
   - Multiple camera support

3. **Processing Queue**
   - Redis-based job queue (Bull.js)
   - Progress tracking and notifications
   - Retry mechanisms and error handling
   - Batch processing capabilities

### Low Priority
1. **Performance Optimizations**
   - Streaming uploads for large files
   - Progressive image loading
   - Audio compression algorithms
   - Caching strategies

2. **Advanced Analytics**
   - Usage statistics and metrics
   - Quality analysis and reporting
   - Performance monitoring
   - User behavior tracking

## Security Considerations

### File Upload Security
- File type validation and sanitization
- Size limits and rate limiting
- Virus scanning integration
- Secure file storage with encryption

### Privacy Protection
- User consent for cloud processing
- Data retention policies
- GDPR compliance features
- Secure deletion and cleanup

### Access Control
- User-based file access restrictions
- Secure API endpoints with authentication
- File sharing permissions
- Audit logging for file operations

## Performance Considerations

### Frontend Optimization
- Lazy loading of media components
- Efficient memory management for large files
- Progressive enhancement for older browsers
- Offline capability with service workers

### Backend Optimization
- Asynchronous file processing
- Efficient storage and retrieval
- Caching strategies for processed results
- Load balancing for processing services

## Browser Compatibility

### Supported Features
- **MediaRecorder API**: Chrome 47+, Firefox 25+, Safari 14+
- **getUserMedia API**: Chrome 53+, Firefox 36+, Safari 11+
- **WebRTC**: Modern browsers with camera/microphone support

### Fallback Strategies
- Feature detection and graceful degradation
- Alternative upload methods for unsupported browsers
- Progressive enhancement approach
- Clear error messages for unsupported features
