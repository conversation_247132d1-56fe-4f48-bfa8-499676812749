import express from 'express';
import multer from 'multer';
import { body, param, query, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';

import { authMiddleware, AuthenticatedRequest } from '@/middleware/auth';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { ValidationError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { mediaService } from '@/services/mediaService';

/**
 * Media API Routes - Foundational routes for voice and image media handling
 * TODO: Implement actual file processing, cloud storage, and advanced features
 */

const router = express.Router();

// ============================================================================
// MIDDLEWARE SETUP
// ============================================================================

// Rate limiting for media uploads
const uploadRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit each IP to 20 uploads per windowMs
  message: {
    error: 'Too many upload attempts',
    message: 'Please wait before uploading more files',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Multer configuration for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit
    files: 1, // Single file upload
  },
  fileFilter: (req, file, cb) => {
    // Allow voice and image files
    const allowedMimeTypes = [
      // Voice formats
      'audio/wav', 'audio/mp3', 'audio/m4a', 'audio/ogg', 'audio/webm',
      // Image formats
      'image/jpeg', 'image/png', 'image/webp', 'image/gif'
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError(`Unsupported file type: ${file.mimetype}`));
    }
  },
});

// ============================================================================
// VOICE MEDIA ROUTES
// ============================================================================

/**
 * @swagger
 * /api/media/voice/upload:
 *   post:
 *     summary: Upload voice recording
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               audio:
 *                 type: string
 *                 format: binary
 *                 description: Audio file
 *               metadata:
 *                 type: string
 *                 description: JSON metadata
 *               autoTranscribe:
 *                 type: boolean
 *                 description: Auto-transcribe audio
 *               language:
 *                 type: string
 *                 description: Transcription language
 *     responses:
 *       201:
 *         description: Voice recording uploaded successfully
 *       400:
 *         description: Validation error
 */
router.post('/voice/upload',
  authMiddleware,
  uploadRateLimiter,
  upload.single('audio'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    if (!req.file) {
      throw new ValidationError('Audio file is required');
    }

    const userId = req.user!.id;
    const { metadata, autoTranscribe, language } = req.body;

    let parsedMetadata = {};
    if (metadata) {
      try {
        parsedMetadata = JSON.parse(metadata);
      } catch (error) {
        throw new ValidationError('Invalid metadata JSON');
      }
    }

    const uploadOptions = {
      userId,
      type: 'voice' as const,
      file: {
        buffer: req.file.buffer,
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
      },
      metadata: parsedMetadata,
      processingOptions: {
        autoTranscribe: autoTranscribe === 'true',
        language: language || 'en',
      },
    };

    const result = await mediaService.uploadMedia(uploadOptions);

    logger.info(`Voice recording uploaded: ${result.id} for user: ${userId}`);

    res.status(201).json({
      success: true,
      message: 'Voice recording uploaded successfully',
      data: {
        mediaId: result.id,
        originalName: result.originalName,
        size: result.size,
        duration: parsedMetadata.duration || null,
        processingStatus: 'pending',
      },
    });
  })
);

/**
 * @swagger
 * /api/media/voice/{id}/transcribe:
 *   post:
 *     summary: Transcribe voice recording
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Media ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               language:
 *                 type: string
 *                 description: Transcription language
 *               includeTimestamps:
 *                 type: boolean
 *                 description: Include word timestamps
 *               enhanceAudio:
 *                 type: boolean
 *                 description: Enhance audio quality
 *     responses:
 *       200:
 *         description: Transcription completed
 *       404:
 *         description: Media not found
 */
router.post('/voice/:id/transcribe',
  authMiddleware,
  [
    param('id').isUUID().withMessage('Invalid media ID'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { id } = req.params;
    const { language, includeTimestamps, enhanceAudio } = req.body;

    const result = await mediaService.processVoiceRecording(id, {
      language,
      includeTimestamps,
      enhanceAudio,
    });

    res.json({
      success: true,
      message: 'Voice recording transcribed successfully',
      data: result,
    });
  })
);

// ============================================================================
// IMAGE MEDIA ROUTES
// ============================================================================

/**
 * @swagger
 * /api/media/image/upload:
 *   post:
 *     summary: Upload image
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file
 *               metadata:
 *                 type: string
 *                 description: JSON metadata
 *               autoOCR:
 *                 type: boolean
 *                 description: Auto-extract text from image
 *               language:
 *                 type: string
 *                 description: OCR language
 *     responses:
 *       201:
 *         description: Image uploaded successfully
 *       400:
 *         description: Validation error
 */
router.post('/image/upload',
  authMiddleware,
  uploadRateLimiter,
  upload.single('image'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    if (!req.file) {
      throw new ValidationError('Image file is required');
    }

    const userId = req.user!.id;
    const { metadata, autoOCR, language } = req.body;

    let parsedMetadata = {};
    if (metadata) {
      try {
        parsedMetadata = JSON.parse(metadata);
      } catch (error) {
        throw new ValidationError('Invalid metadata JSON');
      }
    }

    const uploadOptions = {
      userId,
      type: 'image' as const,
      file: {
        buffer: req.file.buffer,
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
      },
      metadata: parsedMetadata,
      processingOptions: {
        autoOCR: autoOCR === 'true',
        language: language || 'en',
      },
    };

    const result = await mediaService.uploadMedia(uploadOptions);

    logger.info(`Image uploaded: ${result.id} for user: ${userId}`);

    res.status(201).json({
      success: true,
      message: 'Image uploaded successfully',
      data: {
        mediaId: result.id,
        originalName: result.originalName,
        size: result.size,
        dimensions: {
          width: parsedMetadata.width || null,
          height: parsedMetadata.height || null,
        },
        processingStatus: 'pending',
      },
    });
  })
);

/**
 * @swagger
 * /api/media/image/{id}/ocr:
 *   post:
 *     summary: Extract text from image using OCR
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Media ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               language:
 *                 type: string
 *                 description: OCR language
 *               includePositions:
 *                 type: boolean
 *                 description: Include text positions
 *               preprocessImage:
 *                 type: boolean
 *                 description: Preprocess image for better OCR
 *     responses:
 *       200:
 *         description: OCR completed
 *       404:
 *         description: Media not found
 */
router.post('/image/:id/ocr',
  authMiddleware,
  [
    param('id').isUUID().withMessage('Invalid media ID'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { id } = req.params;
    const { language, includePositions, preprocessImage } = req.body;

    const result = await mediaService.processImageOCR(id, {
      language,
      includePositions,
      preprocessImage,
    });

    res.json({
      success: true,
      message: 'Image OCR completed successfully',
      data: result,
    });
  })
);

// ============================================================================
// GENERAL MEDIA ROUTES
// ============================================================================

/**
 * @swagger
 * /api/media/{id}:
 *   get:
 *     summary: Get media file details
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Media ID
 *     responses:
 *       200:
 *         description: Media details retrieved
 *       404:
 *         description: Media not found
 */
router.get('/:id',
  authMiddleware,
  [
    param('id').isUUID().withMessage('Invalid media ID'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { id } = req.params;
    const mediaFile = await mediaService.getMediaFile(id);

    if (!mediaFile) {
      return res.status(404).json({
        success: false,
        message: 'Media file not found',
      });
    }

    // Verify ownership
    if (mediaFile.userId !== req.user!.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied',
      });
    }

    res.json({
      success: true,
      data: {
        id: mediaFile.id,
        type: mediaFile.type,
        originalName: mediaFile.originalName,
        mimeType: mediaFile.mimeType,
        size: mediaFile.size,
        metadata: mediaFile.metadata,
        createdAt: mediaFile.createdAt,
        updatedAt: mediaFile.updatedAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/media/{id}:
 *   delete:
 *     summary: Delete media file
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Media ID
 *     responses:
 *       200:
 *         description: Media deleted successfully
 *       404:
 *         description: Media not found
 */
router.delete('/:id',
  authMiddleware,
  [
    param('id').isUUID().withMessage('Invalid media ID'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { id } = req.params;
    const userId = req.user!.id;

    await mediaService.deleteMediaFile(id, userId);

    logger.info(`Media file deleted: ${id} by user: ${userId}`);

    res.json({
      success: true,
      message: 'Media file deleted successfully',
    });
  })
);

/**
 * @swagger
 * /api/media:
 *   get:
 *     summary: Get user's media files
 *     tags: [Media]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [voice, image]
 *         description: Filter by media type
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Number of items to skip
 *     responses:
 *       200:
 *         description: Media files retrieved
 */
router.get('/',
  authMiddleware,
  [
    query('type').optional().isIn(['voice', 'image']).withMessage('Invalid media type'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const type = req.query.type as string;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    // TODO: Implement actual database query
    const mediaFiles = []; // Placeholder

    res.json({
      success: true,
      data: {
        mediaFiles,
        pagination: {
          limit,
          offset,
          total: mediaFiles.length,
        },
      },
    });
  })
);

export default router;
