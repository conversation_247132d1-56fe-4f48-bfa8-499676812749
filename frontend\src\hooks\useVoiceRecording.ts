import { useState, useRef, useCallback, useEffect } from 'react';
import type { 
  VoiceRecordingConfig, 
  VoiceRecordingState, 
  VoiceRecordingResult,
  VoiceRecordingMetadata 
} from '@/types/media';

/**
 * Custom hook for voice recording functionality
 * TODO: Implement actual MediaRecorder integration, audio processing, and cloud upload
 */

interface UseVoiceRecordingOptions {
  config?: Partial<VoiceRecordingConfig>;
  onRecordingComplete?: (result: VoiceRecordingResult) => void;
  onError?: (error: string) => void;
  autoUpload?: boolean;
}

interface UseVoiceRecordingReturn {
  // State
  recordingState: VoiceRecordingState;
  isSupported: boolean;
  permissions: {
    granted: boolean;
    denied: boolean;
    requesting: boolean;
  };
  
  // Actions
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<VoiceRecordingResult | null>;
  pauseRecording: () => void;
  resumeRecording: () => void;
  cancelRecording: () => void;
  
  // Utilities
  requestPermissions: () => Promise<boolean>;
  getAudioDevices: () => Promise<MediaDeviceInfo[]>;
  setAudioDevice: (deviceId: string) => void;
}

const DEFAULT_CONFIG: VoiceRecordingConfig = {
  quality: 'medium',
  maxDuration: 300, // 5 minutes
  format: 'webm',
  sampleRate: 44100,
  channels: 1,
  noiseReduction: true,
  echoCancellation: true,
  autoGainControl: true,
};

export const useVoiceRecording = (options: UseVoiceRecordingOptions = {}): UseVoiceRecordingReturn => {
  const { config = {}, onRecordingComplete, onError, autoUpload = false } = options;
  
  // Merge config with defaults
  const recordingConfig = { ...DEFAULT_CONFIG, ...config };
  
  // State
  const [recordingState, setRecordingState] = useState<VoiceRecordingState>({
    status: 'idle',
    duration: 0,
    audioLevel: 0,
    fileSize: 0,
  });
  
  const [permissions, setPermissions] = useState({
    granted: false,
    denied: false,
    requesting: false,
  });
  
  const [isSupported, setIsSupported] = useState(false);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>('');
  
  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const startTimeRef = useRef<Date | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioLevelIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check browser support
  useEffect(() => {
    const checkSupport = () => {
      const hasMediaRecorder = typeof MediaRecorder !== 'undefined';
      const hasGetUserMedia = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
      setIsSupported(hasMediaRecorder && hasGetUserMedia);
    };
    
    checkSupport();
  }, []);

  // Request microphone permissions
  const requestPermissions = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      onError?.('Voice recording is not supported in this browser');
      return false;
    }

    setPermissions(prev => ({ ...prev, requesting: true }));

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined,
          sampleRate: recordingConfig.sampleRate,
          channelCount: recordingConfig.channels,
          noiseSuppression: recordingConfig.noiseReduction,
          echoCancellation: recordingConfig.echoCancellation,
          autoGainControl: recordingConfig.autoGainControl,
        } 
      });
      
      // Stop the stream immediately - we just needed to check permissions
      stream.getTracks().forEach(track => track.stop());
      
      setPermissions({ granted: true, denied: false, requesting: false });
      return true;
    } catch (error) {
      console.error('Permission denied:', error);
      setPermissions({ granted: false, denied: true, requesting: false });
      onError?.('Microphone permission denied');
      return false;
    }
  }, [isSupported, selectedDeviceId, recordingConfig, onError]);

  // Get available audio devices
  const getAudioDevices = useCallback(async (): Promise<MediaDeviceInfo[]> => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('Error getting audio devices:', error);
      return [];
    }
  }, []);

  // Set audio device
  const setAudioDevice = useCallback((deviceId: string) => {
    setSelectedDeviceId(deviceId);
  }, []);

  // Start recording
  const startRecording = useCallback(async (): Promise<void> => {
    try {
      if (!permissions.granted) {
        const granted = await requestPermissions();
        if (!granted) return;
      }

      // Get media stream
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined,
          sampleRate: recordingConfig.sampleRate,
          channelCount: recordingConfig.channels,
          noiseSuppression: recordingConfig.noiseReduction,
          echoCancellation: recordingConfig.echoCancellation,
          autoGainControl: recordingConfig.autoGainControl,
        }
      });

      mediaStreamRef.current = stream;

      // Set up audio analysis for level monitoring
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      analyserRef.current.fftSize = 256;

      // Create MediaRecorder
      const mimeType = `audio/${recordingConfig.format}`;
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported(mimeType) ? mimeType : 'audio/webm',
      });

      // Set up event handlers
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        // This will be handled in stopRecording
      };

      // Start recording
      audioChunksRef.current = [];
      startTimeRef.current = new Date();
      mediaRecorderRef.current.start(100); // Collect data every 100ms

      setRecordingState({
        status: 'recording',
        duration: 0,
        audioLevel: 0,
        fileSize: 0,
        startTime: startTimeRef.current,
      });

      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        if (startTimeRef.current) {
          const duration = (Date.now() - startTimeRef.current.getTime()) / 1000;
          setRecordingState(prev => ({ ...prev, duration }));

          // Auto-stop if max duration reached
          if (duration >= recordingConfig.maxDuration) {
            stopRecording();
          }
        }
      }, 100);

      // Start audio level monitoring
      audioLevelIntervalRef.current = setInterval(() => {
        if (analyserRef.current) {
          const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
          const audioLevel = Math.round((average / 255) * 100);
          setRecordingState(prev => ({ ...prev, audioLevel }));
        }
      }, 50);

    } catch (error) {
      console.error('Error starting recording:', error);
      setRecordingState(prev => ({ ...prev, status: 'error', error: 'Failed to start recording' }));
      onError?.('Failed to start recording');
    }
  }, [permissions.granted, requestPermissions, selectedDeviceId, recordingConfig, onError]);

  // Stop recording
  const stopRecording = useCallback(async (): Promise<VoiceRecordingResult | null> => {
    try {
      if (!mediaRecorderRef.current || recordingState.status !== 'recording') {
        return null;
      }

      setRecordingState(prev => ({ ...prev, status: 'processing' }));

      // Stop recording
      mediaRecorderRef.current.stop();
      
      // Clean up intervals
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }
      if (audioLevelIntervalRef.current) {
        clearInterval(audioLevelIntervalRef.current);
        audioLevelIntervalRef.current = null;
      }

      // Stop media stream
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
      }

      // Close audio context
      if (audioContextRef.current) {
        await audioContextRef.current.close();
        audioContextRef.current = null;
      }

      // Create audio blob
      const audioBlob = new Blob(audioChunksRef.current, { 
        type: `audio/${recordingConfig.format}` 
      });

      const endTime = new Date();
      const duration = startTimeRef.current 
        ? (endTime.getTime() - startTimeRef.current.getTime()) / 1000 
        : 0;

      // Create metadata
      const metadata: VoiceRecordingMetadata = {
        duration,
        fileSize: audioBlob.size,
        format: recordingConfig.format,
        sampleRate: recordingConfig.sampleRate,
        channels: recordingConfig.channels,
        recordedAt: startTimeRef.current || new Date(),
        deviceInfo: {
          deviceId: selectedDeviceId || 'default',
          label: 'Default microphone', // TODO: Get actual device label
          kind: 'audioinput',
        },
        qualityMetrics: {
          averageLevel: 0, // TODO: Calculate from recorded data
          peakLevel: 0,    // TODO: Calculate from recorded data
          silenceRatio: 0, // TODO: Calculate from recorded data
        },
      };

      // Create result
      const result: VoiceRecordingResult = {
        id: crypto.randomUUID(),
        audioBlob,
        metadata,
        processingStatus: 'pending',
      };

      setRecordingState({
        status: 'completed',
        duration,
        audioLevel: 0,
        fileSize: audioBlob.size,
        startTime: startTimeRef.current,
        endTime,
      });

      // TODO: Auto-upload if enabled
      if (autoUpload) {
        // Implement upload logic
      }

      onRecordingComplete?.(result);
      return result;

    } catch (error) {
      console.error('Error stopping recording:', error);
      setRecordingState(prev => ({ ...prev, status: 'error', error: 'Failed to stop recording' }));
      onError?.('Failed to stop recording');
      return null;
    }
  }, [recordingState.status, recordingConfig.format, selectedDeviceId, autoUpload, onRecordingComplete, onError]);

  // Pause recording
  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && recordingState.status === 'recording') {
      mediaRecorderRef.current.pause();
      setRecordingState(prev => ({ ...prev, status: 'paused' }));
    }
  }, [recordingState.status]);

  // Resume recording
  const resumeRecording = useCallback(() => {
    if (mediaRecorderRef.current && recordingState.status === 'paused') {
      mediaRecorderRef.current.resume();
      setRecordingState(prev => ({ ...prev, status: 'recording' }));
    }
  }, [recordingState.status]);

  // Cancel recording
  const cancelRecording = useCallback(() => {
    // Clean up everything
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;
    }
    
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    
    if (audioLevelIntervalRef.current) {
      clearInterval(audioLevelIntervalRef.current);
      audioLevelIntervalRef.current = null;
    }

    audioChunksRef.current = [];
    startTimeRef.current = null;

    setRecordingState({
      status: 'idle',
      duration: 0,
      audioLevel: 0,
      fileSize: 0,
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelRecording();
    };
  }, [cancelRecording]);

  return {
    recordingState,
    isSupported,
    permissions,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    cancelRecording,
    requestPermissions,
    getAudioDevices,
    setAudioDevice,
  };
};
