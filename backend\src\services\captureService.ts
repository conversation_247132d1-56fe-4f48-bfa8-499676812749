import { executeQuery, executeNeo4jQuery } from '@/config/database';
import { logger } from '@/utils/logger';
import { nlpService } from './nlpService';
import { ocrService } from './ocrService';
import { speechToTextService } from './speechToTextService';
import { emailService } from './emailService';
import { CustomError, ValidationError } from '@/middleware/errorHandler';

export interface CaptureInput {
  userId: string;
  type: 'text' | 'voice' | 'image' | 'email' | 'web_clip';
  content: string;
  metadata?: any;
  filePath?: string;
  sourceUrl?: string;
}

export interface ProcessedCapture {
  id: string;
  userId: string;
  type: string;
  originalContent: string;
  processedContent: string;
  extractedEntities: any[];
  suggestedTags: string[];
  relatedNodes: any[];
  confidence: number;
  metadata: any;
  createdAt: Date;
}

class CaptureService {
  /**
   * Process a new capture input
   */
  async processCapture(input: CaptureInput): Promise<ProcessedCapture> {
    try {
      logger.info(`Processing capture for user ${input.userId}, type: ${input.type}`);
      
      // Validate input
      this.validateCaptureInput(input);
      
      // Store initial capture
      const captureId = await this.storeCaptureRecord(input);
      
      // Process based on type
      let processedContent = input.content;
      let extractedData: any = {};
      
      switch (input.type) {
        case 'text':
          extractedData = await this.processTextCapture(input.content);
          break;
        case 'voice':
          processedContent = await speechToTextService.transcribe(input.filePath!);
          extractedData = await this.processTextCapture(processedContent);
          break;
        case 'image':
          processedContent = await ocrService.extractText(input.filePath!);
          extractedData = await this.processTextCapture(processedContent);
          break;
        case 'email':
          extractedData = await emailService.processEmail(input.content);
          processedContent = extractedData.processedContent;
          break;
        case 'web_clip':
          extractedData = await this.processWebClip(input.content, input.sourceUrl!);
          processedContent = extractedData.processedContent;
          break;
        default:
          throw new ValidationError(`Unsupported capture type: ${input.type}`);
      }
      
      // Update capture with processed data
      await this.updateCaptureRecord(captureId, processedContent, extractedData);
      
      // Create knowledge graph connections
      const relatedNodes = await this.createKnowledgeConnections(
        input.userId,
        captureId,
        processedContent,
        extractedData
      );
      
      // Generate suggestions
      const suggestions = await this.generateSuggestions(
        input.userId,
        processedContent,
        extractedData
      );
      
      const result: ProcessedCapture = {
        id: captureId,
        userId: input.userId,
        type: input.type,
        originalContent: input.content,
        processedContent,
        extractedEntities: extractedData.entities || [],
        suggestedTags: extractedData.tags || [],
        relatedNodes,
        confidence: extractedData.confidence || 0.8,
        metadata: { ...input.metadata, ...extractedData.metadata },
        createdAt: new Date()
      };
      
      logger.info(`Capture processed successfully: ${captureId}`);
      return result;
      
    } catch (error) {
      logger.error('Error processing capture:', error);
      throw error;
    }
  }
  
  /**
   * Process text content using NLP
   */
  private async processTextCapture(content: string): Promise<any> {
    try {
      const nlpResult = await nlpService.analyzeText(content);
      
      return {
        entities: nlpResult.entities,
        tags: nlpResult.suggestedTags,
        sentiment: nlpResult.sentiment,
        topics: nlpResult.topics,
        actionItems: nlpResult.actionItems,
        confidence: nlpResult.confidence,
        metadata: {
          wordCount: content.split(' ').length,
          language: nlpResult.language,
          readingTime: Math.ceil(content.split(' ').length / 200) // minutes
        }
      };
    } catch (error) {
      logger.error('Error processing text capture:', error);
      throw new CustomError('Failed to process text content');
    }
  }
  
  /**
   * Process web clip content
   */
  private async processWebClip(content: string, sourceUrl: string): Promise<any> {
    try {
      // Extract main content and metadata from web page
      const webData = await this.extractWebContent(content, sourceUrl);
      const nlpResult = await nlpService.analyzeText(webData.mainContent);
      
      return {
        processedContent: webData.mainContent,
        entities: nlpResult.entities,
        tags: [...nlpResult.suggestedTags, ...webData.tags],
        sentiment: nlpResult.sentiment,
        topics: nlpResult.topics,
        confidence: nlpResult.confidence,
        metadata: {
          sourceUrl,
          title: webData.title,
          author: webData.author,
          publishDate: webData.publishDate,
          domain: new URL(sourceUrl).hostname,
          wordCount: webData.mainContent.split(' ').length
        }
      };
    } catch (error) {
      logger.error('Error processing web clip:', error);
      throw new CustomError('Failed to process web clip');
    }
  }
  
  /**
   * Extract content from web page HTML
   */
  private async extractWebContent(html: string, url: string): Promise<any> {
    // This is a simplified implementation
    // In production, you'd use libraries like Readability.js or Mercury Parser
    
    const titleMatch = html.match(/<title>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1] : '';
    
    // Remove HTML tags and extract text content
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    // Extract meta tags
    const metaTags: string[] = [];
    const metaMatches = html.match(/<meta[^>]*name="keywords"[^>]*content="([^"]*)"[^>]*>/gi);
    if (metaMatches) {
      metaMatches.forEach(match => {
        const contentMatch = match.match(/content="([^"]*)"/i);
        if (contentMatch) {
          metaTags.push(...contentMatch[1].split(',').map(tag => tag.trim()));
        }
      });
    }
    
    return {
      title,
      mainContent: textContent.substring(0, 5000), // Limit content length
      tags: metaTags,
      author: this.extractAuthor(html),
      publishDate: this.extractPublishDate(html)
    };
  }
  
  /**
   * Extract author from HTML
   */
  private extractAuthor(html: string): string | null {
    const authorPatterns = [
      /<meta[^>]*name="author"[^>]*content="([^"]*)"[^>]*>/i,
      /<meta[^>]*property="article:author"[^>]*content="([^"]*)"[^>]*>/i,
      /<span[^>]*class="[^"]*author[^"]*"[^>]*>(.*?)<\/span>/i
    ];
    
    for (const pattern of authorPatterns) {
      const match = html.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    
    return null;
  }
  
  /**
   * Extract publish date from HTML
   */
  private extractPublishDate(html: string): string | null {
    const datePatterns = [
      /<meta[^>]*property="article:published_time"[^>]*content="([^"]*)"[^>]*>/i,
      /<time[^>]*datetime="([^"]*)"[^>]*>/i,
      /<meta[^>]*name="date"[^>]*content="([^"]*)"[^>]*>/i
    ];
    
    for (const pattern of datePatterns) {
      const match = html.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    
    return null;
  }
  
  /**
   * Store initial capture record
   */
  private async storeCaptureRecord(input: CaptureInput): Promise<string> {
    const query = `
      INSERT INTO captures (user_id, type, content, metadata, file_path, source_url)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id
    `;
    
    const result = await executeQuery(query, [
      input.userId,
      input.type,
      input.content,
      JSON.stringify(input.metadata || {}),
      input.filePath,
      input.sourceUrl
    ]);
    
    return result.rows[0].id;
  }
  
  /**
   * Update capture record with processed data
   */
  private async updateCaptureRecord(
    captureId: string,
    processedContent: string,
    extractedData: any
  ): Promise<void> {
    const query = `
      UPDATE captures 
      SET processed_content = $1, metadata = $2, is_processed = true, processed_at = NOW()
      WHERE id = $3
    `;
    
    await executeQuery(query, [
      processedContent,
      JSON.stringify(extractedData),
      captureId
    ]);
  }
  
  /**
   * Create knowledge graph connections
   */
  private async createKnowledgeConnections(
    userId: string,
    captureId: string,
    content: string,
    extractedData: any
  ): Promise<any[]> {
    try {
      // Create capture node in Neo4j
      const createCaptureQuery = `
        CREATE (c:Capture {
          id: $captureId,
          userId: $userId,
          content: $content,
          createdAt: datetime()
        })
        RETURN c
      `;
      
      await executeNeo4jQuery(createCaptureQuery, {
        captureId,
        userId,
        content: content.substring(0, 1000) // Limit content length
      });
      
      // Create entity nodes and relationships
      const relatedNodes = [];
      
      if (extractedData.entities) {
        for (const entity of extractedData.entities) {
          const entityQuery = `
            MERGE (e:Entity {name: $entityName, type: $entityType})
            WITH e
            MATCH (c:Capture {id: $captureId})
            MERGE (c)-[:MENTIONS {confidence: $confidence}]->(e)
            RETURN e
          `;
          
          const entityResult = await executeNeo4jQuery(entityQuery, {
            entityName: entity.name,
            entityType: entity.type,
            captureId,
            confidence: entity.confidence || 0.8
          });
          
          relatedNodes.push(...entityResult.records.map(r => r.get('e').properties));
        }
      }
      
      // Find similar existing captures
      const similarityQuery = `
        MATCH (c1:Capture {id: $captureId})
        MATCH (c2:Capture {userId: $userId})
        WHERE c1 <> c2
        WITH c1, c2, 
             size(split(toLower(c1.content), ' ')) as words1,
             size(split(toLower(c2.content), ' ')) as words2,
             size([word IN split(toLower(c1.content), ' ') WHERE word IN split(toLower(c2.content), ' ')]) as commonWords
        WHERE commonWords > 2 AND commonWords * 2.0 / (words1 + words2) > 0.3
        MERGE (c1)-[:SIMILAR_TO {similarity: commonWords * 2.0 / (words1 + words2)}]->(c2)
        RETURN c2, commonWords * 2.0 / (words1 + words2) as similarity
        LIMIT 5
      `;
      
      const similarResults = await executeNeo4jQuery(similarityQuery, {
        captureId,
        userId
      });
      
      relatedNodes.push(...similarResults.records.map(r => ({
        ...r.get('c2').properties,
        similarity: r.get('similarity')
      })));
      
      return relatedNodes;
      
    } catch (error) {
      logger.error('Error creating knowledge connections:', error);
      return [];
    }
  }
  
  /**
   * Generate suggestions based on processed content
   */
  private async generateSuggestions(
    userId: string,
    content: string,
    extractedData: any
  ): Promise<any> {
    try {
      // This would integrate with ML models to generate suggestions
      // For now, return basic suggestions based on extracted data
      
      const suggestions = {
        tasks: [],
        goals: [],
        connections: [],
        templates: []
      };
      
      // Generate task suggestions from action items
      if (extractedData.actionItems) {
        suggestions.tasks = extractedData.actionItems.map((item: any) => ({
          title: item.text,
          priority: item.urgency || 'medium',
          estimatedDuration: item.estimatedDuration || 30
        }));
      }
      
      // Generate goal connections based on entities
      if (extractedData.entities) {
        const goalQuery = `
          SELECT g.id, g.title, g.description
          FROM goals g
          WHERE g.user_id = $1 AND g.status = 'active'
          AND (
            to_tsvector('english', g.title || ' ' || g.description) @@ plainto_tsquery('english', $2)
          )
          LIMIT 3
        `;
        
        const entityText = extractedData.entities.map((e: any) => e.name).join(' ');
        const goalResults = await executeQuery(goalQuery, [userId, entityText]);
        
        suggestions.goals = goalResults.rows;
      }
      
      return suggestions;
      
    } catch (error) {
      logger.error('Error generating suggestions:', error);
      return { tasks: [], goals: [], connections: [], templates: [] };
    }
  }
  
  /**
   * Validate capture input
   */
  private validateCaptureInput(input: CaptureInput): void {
    if (!input.userId) {
      throw new ValidationError('User ID is required');
    }
    
    if (!input.type) {
      throw new ValidationError('Capture type is required');
    }
    
    if (!input.content && !input.filePath) {
      throw new ValidationError('Content or file path is required');
    }
    
    const validTypes = ['text', 'voice', 'image', 'email', 'web_clip'];
    if (!validTypes.includes(input.type)) {
      throw new ValidationError(`Invalid capture type. Must be one of: ${validTypes.join(', ')}`);
    }
    
    if ((input.type === 'voice' || input.type === 'image') && !input.filePath) {
      throw new ValidationError(`File path is required for ${input.type} captures`);
    }
    
    if (input.type === 'web_clip' && !input.sourceUrl) {
      throw new ValidationError('Source URL is required for web clip captures');
    }
  }
  
  /**
   * Get capture history for a user
   */
  async getCaptureHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0,
    type?: string
  ): Promise<any[]> {
    let query = `
      SELECT id, type, content, processed_content, metadata, 
             file_path, source_url, is_processed, created_at, processed_at
      FROM captures
      WHERE user_id = $1
    `;
    
    const params = [userId];
    
    if (type) {
      query += ` AND type = $${params.length + 1}`;
      params.push(type);
    }
    
    query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit.toString(), offset.toString());
    
    const result = await executeQuery(query, params);
    return result.rows;
  }
  
  /**
   * Search captures
   */
  async searchCaptures(userId: string, searchTerm: string, limit: number = 20): Promise<any[]> {
    const query = `
      SELECT id, type, content, processed_content, metadata, 
             file_path, source_url, created_at,
             ts_rank(to_tsvector('english', content || ' ' || COALESCE(processed_content, '')), 
                     plainto_tsquery('english', $2)) as rank
      FROM captures
      WHERE user_id = $1 
      AND to_tsvector('english', content || ' ' || COALESCE(processed_content, '')) @@ plainto_tsquery('english', $2)
      ORDER BY rank DESC, created_at DESC
      LIMIT $3
    `;
    
    const result = await executeQuery(query, [userId, searchTerm, limit]);
    return result.rows;
  }
}

export const captureService = new CaptureService();
