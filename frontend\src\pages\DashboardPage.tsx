import React, { useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Paper,
  Avatar,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  CameraAltRounded,
  TrackChangesRounded,
  AssignmentRounded,
  TrendingUpRounded,
  AddRounded,
  HistoryRounded,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentUser } from '@/store/slices/authSlice';
import { setBreadcrumbs } from '@/store/slices/uiSlice';
import { useGetUserStatsQuery } from '@/store/api/usersApi';
import { useGetCaptureHistoryQuery } from '@/store/api/captureApi';
import { useGetGoalsQuery } from '@/store/api/goalsApi';
import { useGetTasksQuery } from '@/store/api/tasksApi';
import { InlineLoading } from '@/components/LoadingScreen';
import { QuickCaptureWidget } from '@/components/dashboard/QuickCaptureWidget';
import { RecentCapturesWidget } from '@/components/dashboard/RecentCapturesWidget';
import { GoalsOverviewWidget } from '@/components/dashboard/GoalsOverviewWidget';
import { TasksOverviewWidget } from '@/components/dashboard/TasksOverviewWidget';

export const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectCurrentUser);

  const { data: statsData, isLoading: statsLoading } = useGetUserStatsQuery();
  const { data: capturesData, isLoading: capturesLoading } = useGetCaptureHistoryQuery({ limit: 5 });
  const { data: goalsData, isLoading: goalsLoading } = useGetGoalsQuery({ status: 'active', limit: 5 });
  const { data: tasksData, isLoading: tasksLoading } = useGetTasksQuery({ status: 'todo', limit: 5 });

  useEffect(() => {
    dispatch(setBreadcrumbs([{ label: 'Dashboard' }]));
  }, [dispatch]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const stats = statsData?.stats;

  return (
    <Box>
      {/* Welcome Header */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          borderRadius: 3,
        }}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'rgba(255,255,255,0.2)',
                fontSize: '1.5rem',
              }}
            >
              {user?.firstName?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={700} gutterBottom>
                {getGreeting()}, {user?.firstName || user?.email?.split('@')[0]}!
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                Ready to boost your productivity today?
              </Typography>
            </Box>
          </Box>
          <Box display="flex" gap={2}>
            <Button
              variant="contained"
              startIcon={<CameraAltRounded />}
              onClick={() => navigate('/capture')}
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' },
              }}
            >
              Quick Capture
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Captures
                  </Typography>
                  <Typography variant="h4" fontWeight={600}>
                    {statsLoading ? (
                      <InlineLoading size={20} message="" />
                    ) : (
                      stats?.captures?.reduce((sum, c) => sum + c.count, 0) || 0
                    )}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <CameraAltRounded />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Goals
                  </Typography>
                  <Typography variant="h4" fontWeight={600}>
                    {statsLoading ? (
                      <InlineLoading size={20} message="" />
                    ) : (
                      stats?.goals?.find(g => g.status === 'active')?.count || 0
                    )}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrackChangesRounded />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Pending Tasks
                  </Typography>
                  <Typography variant="h4" fontWeight={600}>
                    {statsLoading ? (
                      <InlineLoading size={20} message="" />
                    ) : (
                      stats?.tasks?.filter(t => t.status === 'todo').reduce((sum, t) => sum + t.count, 0) || 0
                    )}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <AssignmentRounded />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Completed Tasks
                  </Typography>
                  <Typography variant="h4" fontWeight={600}>
                    {statsLoading ? (
                      <InlineLoading size={20} message="" />
                    ) : (
                      stats?.tasks?.filter(t => t.status === 'completed').reduce((sum, t) => sum + t.count, 0) || 0
                    )}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <TrendingUpRounded />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Left Column */}
        <Grid item xs={12} lg={8}>
          <Grid container spacing={3}>
            {/* Quick Capture */}
            <Grid item xs={12}>
              <QuickCaptureWidget />
            </Grid>

            {/* Recent Captures */}
            <Grid item xs={12} md={6}>
              <RecentCapturesWidget
                captures={capturesData?.captures || []}
                loading={capturesLoading}
              />
            </Grid>

            {/* Tasks Overview */}
            <Grid item xs={12} md={6}>
              <TasksOverviewWidget
                tasks={tasksData?.tasks || []}
                loading={tasksLoading}
              />
            </Grid>
          </Grid>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} lg={4}>
          <Grid container spacing={3}>
            {/* Goals Overview */}
            <Grid item xs={12}>
              <GoalsOverviewWidget
                goals={goalsData?.goals || []}
                loading={goalsLoading}
              />
            </Grid>

            {/* Quick Actions */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight={600} gutterBottom>
                    Quick Actions
                  </Typography>
                  <Box display="flex" flexDirection="column" gap={1}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<AddRounded />}
                      onClick={() => navigate('/goals')}
                    >
                      Create Goal
                    </Button>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<AssignmentRounded />}
                      onClick={() => navigate('/tasks')}
                    >
                      Add Task
                    </Button>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<HistoryRounded />}
                      onClick={() => navigate('/capture/history')}
                    >
                      View History
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Productivity Tip */}
            <Grid item xs={12}>
              <Card sx={{ bgcolor: 'info.main', color: 'info.contrastText' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight={600} gutterBottom>
                    💡 Productivity Tip
                  </Typography>
                  <Typography variant="body2">
                    Start your day by capturing your top 3 priorities. This helps maintain focus and ensures important tasks don't get overlooked.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};
