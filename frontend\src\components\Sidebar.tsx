import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography,
  Chip,
} from '@mui/material';
import {
  DashboardRounded,
  CameraAltRounded,
  HistoryRounded,
  TrackChangesRounded,
  AssignmentRounded,
  PersonRounded,
  SettingsRounded,
  AnalyticsRounded,
  AutoAwesomeRounded,
} from '@mui/icons-material';

import { useAppSelector } from '@/store/hooks';
import { selectCurrentUser } from '@/store/slices/authSlice';

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
  badge?: string | number;
  divider?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: <DashboardRounded />,
  },
  {
    id: 'capture',
    label: 'Quick Capture',
    path: '/capture',
    icon: <CameraAltRounded />,
    divider: true,
  },
  {
    id: 'capture-history',
    label: 'Capture History',
    path: '/capture/history',
    icon: <HistoryRounded />,
  },
  {
    id: 'goals',
    label: 'Goals',
    path: '/goals',
    icon: <TrackChangesRounded />,
  },
  {
    id: 'tasks',
    label: 'Tasks',
    path: '/tasks',
    icon: <AssignmentRounded />,
    divider: true,
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/analytics',
    icon: <AnalyticsRounded />,
    badge: 'Pro',
  },
  {
    id: 'templates',
    label: 'Smart Templates',
    path: '/templates',
    icon: <AutoAwesomeRounded />,
    badge: 'New',
    divider: true,
  },
  {
    id: 'profile',
    label: 'Profile',
    path: '/profile',
    icon: <PersonRounded />,
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: <SettingsRounded />,
  },
];

export const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const user = useAppSelector(selectCurrentUser);

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const isActive = (path: string) => {
    if (path === '/dashboard') {
      return location.pathname === '/' || location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* User Info */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          Welcome back,
        </Typography>
        <Typography variant="h6" fontWeight={600} noWrap>
          {user?.firstName || user?.email?.split('@')[0] || 'User'}
        </Typography>
        {user?.role && user.role !== 'user' && (
          <Chip
            label={user.role.toUpperCase()}
            size="small"
            color="primary"
            variant="outlined"
            sx={{ mt: 1 }}
          />
        )}
      </Box>

      {/* Navigation */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ py: 1 }}>
          {navigationItems.map((item) => (
            <React.Fragment key={item.id}>
              <ListItemButton
                selected={isActive(item.path)}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  mx: 1,
                  borderRadius: 2,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 40,
                    color: isActive(item.path) ? 'inherit' : 'text.secondary',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive(item.path) ? 600 : 400,
                  }}
                />
                {item.badge && (
                  <Chip
                    label={item.badge}
                    size="small"
                    color={item.badge === 'Pro' ? 'secondary' : 'success'}
                    variant="outlined"
                    sx={{
                      height: 20,
                      fontSize: '0.625rem',
                      '& .MuiChip-label': {
                        px: 1,
                      },
                    }}
                  />
                )}
              </ListItemButton>
              {item.divider && <Divider sx={{ my: 1 }} />}
            </React.Fragment>
          ))}
        </List>
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" display="block">
          MindSync v1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Your productivity companion
        </Typography>
      </Box>
    </Box>
  );
};
