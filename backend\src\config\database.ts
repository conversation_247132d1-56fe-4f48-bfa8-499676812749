import { Pool } from 'pg';
import neo4j, { Driver } from 'neo4j-driver';
import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';

// PostgreSQL connection
let pgPool: Pool;

// Neo4j connection
let neo4jDriver: Driver;

// Redis connection
let redisClient: RedisClientType;

/**
 * Initialize PostgreSQL connection
 */
export async function connectPostgreSQL(): Promise<Pool> {
  try {
    pgPool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Test the connection
    const client = await pgPool.connect();
    await client.query('SELECT NOW()');
    client.release();

    logger.info('✅ PostgreSQL connected successfully');
    return pgPool;
  } catch (error) {
    logger.error('❌ PostgreSQL connection failed:', error);
    throw error;
  }
}

/**
 * Initialize Neo4j connection
 */
export async function connectNeo4j(): Promise<Driver> {
  try {
    neo4jDriver = neo4j.driver(
      process.env.NEO4J_URI || 'bolt://localhost:7687',
      neo4j.auth.basic(
        process.env.NEO4J_USER || 'neo4j',
        process.env.NEO4J_PASSWORD || 'password'
      ),
      {
        maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
        maxConnectionPoolSize: 50,
        connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
      }
    );

    // Test the connection
    const session = neo4jDriver.session();
    await session.run('RETURN 1');
    await session.close();

    logger.info('✅ Neo4j connected successfully');
    return neo4jDriver;
  } catch (error) {
    logger.error('❌ Neo4j connection failed:', error);
    throw error;
  }
}

/**
 * Initialize Redis connection
 */
export async function connectRedis(): Promise<RedisClientType> {
  try {
    redisClient = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      socket: {
        connectTimeout: 5000,
        lazyConnect: true,
      },
    });

    redisClient.on('error', (err) => {
      logger.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      logger.info('✅ Redis connected successfully');
    });

    redisClient.on('reconnecting', () => {
      logger.info('🔄 Redis reconnecting...');
    });

    await redisClient.connect();
    return redisClient;
  } catch (error) {
    logger.error('❌ Redis connection failed:', error);
    throw error;
  }
}

/**
 * Connect to all databases
 */
export async function connectDatabases(): Promise<void> {
  try {
    await Promise.all([
      connectPostgreSQL(),
      connectNeo4j(),
      connectRedis(),
    ]);
    logger.info('🎉 All databases connected successfully');
  } catch (error) {
    logger.error('💥 Database connection failed:', error);
    throw error;
  }
}

/**
 * Close all database connections
 */
export async function closeDatabases(): Promise<void> {
  try {
    const promises = [];

    if (pgPool) {
      promises.push(pgPool.end());
    }

    if (neo4jDriver) {
      promises.push(neo4jDriver.close());
    }

    if (redisClient) {
      promises.push(redisClient.quit());
    }

    await Promise.all(promises);
    logger.info('🔌 All database connections closed');
  } catch (error) {
    logger.error('❌ Error closing database connections:', error);
    throw error;
  }
}

/**
 * Get PostgreSQL pool instance
 */
export function getPostgreSQLPool(): Pool {
  if (!pgPool) {
    throw new Error('PostgreSQL pool not initialized. Call connectPostgreSQL() first.');
  }
  return pgPool;
}

/**
 * Get Neo4j driver instance
 */
export function getNeo4jDriver(): Driver {
  if (!neo4jDriver) {
    throw new Error('Neo4j driver not initialized. Call connectNeo4j() first.');
  }
  return neo4jDriver;
}

/**
 * Get Redis client instance
 */
export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return redisClient;
}

/**
 * Execute PostgreSQL query with error handling
 */
export async function executeQuery(text: string, params?: any[]): Promise<any> {
  const client = await pgPool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    logger.error('PostgreSQL query error:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Execute Neo4j query with error handling
 */
export async function executeNeo4jQuery(cypher: string, params?: any): Promise<any> {
  const session = neo4jDriver.session();
  try {
    const result = await session.run(cypher, params);
    return result;
  } catch (error) {
    logger.error('Neo4j query error:', error);
    throw error;
  } finally {
    await session.close();
  }
}

/**
 * Redis cache helper functions
 */
export const cache = {
  async get(key: string): Promise<string | null> {
    try {
      return await redisClient.get(key);
    } catch (error) {
      logger.error('Redis GET error:', error);
      return null;
    }
  },

  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await redisClient.setEx(key, ttl, value);
      } else {
        await redisClient.set(key, value);
      }
    } catch (error) {
      logger.error('Redis SET error:', error);
    }
  },

  async del(key: string): Promise<void> {
    try {
      await redisClient.del(key);
    } catch (error) {
      logger.error('Redis DEL error:', error);
    }
  },

  async exists(key: string): Promise<boolean> {
    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS error:', error);
      return false;
    }
  }
};
