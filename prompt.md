Create a comprehensive productivity application called "MindSync" - a second brain system for enhanced productivity. This should be a complete, production-ready project with the following specifications:

**Project Overview:**
- **Name**: MindSync
- **Tagline**: "Your second brain for productivity"
- **Target**: Knowledge workers, entrepreneurs, and productivity enthusiasts who struggle with information overload

**Core Problem Statement:**
Build a system that solves these specific pain points:
1. Information fragmentation across multiple platforms (emails, notes, tasks, documents)
2. Inability to capture spontaneous ideas quickly and effectively
3. Disconnect between daily tasks and long-term strategic goals
4. Decision fatigue from constantly choosing what to work on next

**Required Features (MVP):**
1. **Universal Capture Hub**: Multi-modal input system supporting:
   - Text input with natural language processing
   - Voice-to-text conversion using free APIs
   - Image OCR for document/whiteboard capture
   - Email integration for task extraction
   - Web clipper functionality

2. **Intelligent Knowledge Graph**: 
   - Visual relationship mapping between tasks, projects, and knowledge
   - Automatic connection discovery using semantic analysis
   - Graph-based navigation and exploration

3. **Goal Alignment Engine**:
   - Hierarchical goal structure (vision → objectives → key results → tasks)
   - Daily task recommendation based on goal priority
   - Progress tracking with visual indicators

4. **Cognitive Load Manager**:
   - Task complexity scoring algorithm
   - Workload distribution recommendations
   - Break and focus session suggestions

5. **Smart Templates & Pattern Recognition**:
   - Project template generation from successful patterns
   - Workflow automation suggestions
   - Recurring task pattern detection

6. **Emotion-Aware Scheduling**:
   - Mood tracking integration
   - Energy level-based task recommendations
   - Optimal timing suggestions for different task types

**Technical Implementation Requirements:**
- **Frontend**: Modern web application (React/Vue.js with responsive design)
- **Backend**: RESTful API with real-time capabilities (Node.js/Express or Python/FastAPI)
- **Database**: Graph database (Neo4j community edition) for relationships + traditional DB for structured data
- **AI/ML**: Use free APIs (Hugging Face models) for NLP and pattern recognition
- **Storage**: Local-first with cloud sync capabilities
- **Authentication**: JWT-based with social login options
- **Integrations**: APIs for popular tools (Notion, Obsidian, Google Calendar, Gmail)

**Deliverables Required:**
My github: HectorTa1989.
1. **GitHub README.md** containing:
   - 5 alternative product names with verified available domain names
   - Complete system architecture diagram (Mermaid syntax)
   - User workflow diagram (Mermaid syntax)
   - Detailed project structure with file organization
   - Installation and setup instructions
   - API documentation overview

2. **Complete Codebase** with:
   - Full project structure implementation
   - Each file provided as separate code blocks with exact file paths
   - Custom algorithms for core features (avoid heavy external dependencies)
   - Integration with free APIs where appropriate
   - Proper error handling and validation
   - Unit tests for critical functions

3. **Git Commit Strategy**:
   - Individual commit message for each file
   - Logical commit sequence for proper version control
   - Clear, descriptive commit messages following conventional commit format

**Constraints & Preferences:**
- Prioritize custom algorithms over heavy external libraries
- Use free APIs and open-source tools only
- Ensure cross-platform compatibility
- Implement offline-first functionality where possible
- Focus on performance and scalability
- Include comprehensive documentation and comments

**Success Criteria:**
- Functional MVP that demonstrates all core features
- Clean, maintainable code architecture
- Proper separation of concerns
- Scalable database design
- Intuitive user interface
- Comprehensive documentation

Please create this as a complete, deployable application that can serve as a foundation for a productivity startup.