import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';

export const ProfilePage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Profile' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        User Profile
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your profile information and preferences.
      </Typography>

      <Alert severity="info">
        <Typography variant="body2">
          Profile management interface is coming soon! This will include profile editing, avatar upload, and account settings.
        </Typography>
      </Alert>
    </Box>
  );
};
