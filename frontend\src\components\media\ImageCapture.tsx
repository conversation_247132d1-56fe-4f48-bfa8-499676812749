import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  IconButton,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Slider,
  Chip,
  Paper,
} from '@mui/material';
import {
  PhotoCameraRounded,
  CameraAltRounded,
  FlipCameraAndroidRounded,
  SettingsRounded,
  UploadFileRounded,
  CropRounded,
  CloseRounded,
} from '@mui/icons-material';

import { useImageCapture } from '@/hooks/useImageCapture';
import { useNotification } from '@/components/NotificationProvider';
import type { ImageCaptureResult, ImageCaptureConfig } from '@/types/media';

/**
 * Image Capture Component
 * TODO: Implement image preview, editing tools, and upload functionality
 */

interface ImageCaptureProps {
  onImageCaptured?: (result: ImageCaptureResult) => void;
  onError?: (error: string) => void;
  autoUpload?: boolean;
  className?: string;
}

export const ImageCapture: React.FC<ImageCaptureProps> = ({
  onImageCaptured,
  onError,
  autoUpload = false,
  className,
}) => {
  const { showSuccess, showError } = useNotification();
  
  // State
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [cameraDevices, setCameraDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');
  const [captureConfig, setCaptureConfig] = useState<Partial<ImageCaptureConfig>>({
    quality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080,
    format: 'jpeg',
    enableCompression: true,
    compressionQuality: 0.8,
  });

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Image capture hook
  const {
    isSupported,
    isCameraActive,
    permissions,
    startCamera,
    stopCamera,
    captureImage,
    uploadFromFile,
    requestPermissions,
    getCameraDevices,
    setCameraDevice,
    videoRef,
  } = useImageCapture({
    config: captureConfig,
    onImageCaptured: (result) => {
      showSuccess('Image captured successfully!');
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(result.imageBlob);
      setCapturedImage(previewUrl);
      setPreviewOpen(true);
      
      onImageCaptured?.(result);
    },
    onError: (error) => {
      showError(error);
      onError?.(error);
    },
    autoUpload,
  });

  // Load camera devices
  const loadCameraDevices = async () => {
    const devices = await getCameraDevices();
    setCameraDevices(devices);
    if (devices.length > 0 && !selectedDevice) {
      setSelectedDevice(devices[0].deviceId);
      setCameraDevice(devices[0].deviceId);
    }
  };

  // Handle camera start
  const handleStartCamera = async () => {
    try {
      if (!permissions.granted) {
        const granted = await requestPermissions();
        if (!granted) return;
      }
      
      await loadCameraDevices();
      await startCamera(facingMode);
    } catch (error) {
      console.error('Failed to start camera:', error);
    }
  };

  // Handle device change
  const handleDeviceChange = (deviceId: string) => {
    setSelectedDevice(deviceId);
    setCameraDevice(deviceId);
    if (isCameraActive) {
      startCamera(facingMode);
    }
  };

  // Handle facing mode toggle
  const handleFacingModeToggle = () => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user';
    setFacingMode(newFacingMode);
    if (isCameraActive) {
      startCamera(newFacingMode);
    }
  };

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFromFile(file);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle capture
  const handleCapture = async () => {
    try {
      await captureImage();
    } catch (error) {
      console.error('Failed to capture image:', error);
    }
  };

  // Close preview
  const handleClosePreview = () => {
    setPreviewOpen(false);
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
      setCapturedImage(null);
    }
  };

  if (!isSupported) {
    return (
      <Alert severity="error">
        Camera is not supported in this browser. Please use a modern browser with camera support.
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Image Capture
          </Typography>
          <Box display="flex" gap={1}>
            <IconButton
              size="small"
              onClick={() => setSettingsOpen(true)}
              disabled={isCameraActive}
            >
              <SettingsRounded />
            </IconButton>
          </Box>
        </Box>

        {/* Permission Request */}
        {!permissions.granted && !permissions.denied && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2" gutterBottom>
              Camera access is required for image capture.
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={requestPermissions}
              disabled={permissions.requesting}
            >
              {permissions.requesting ? 'Requesting...' : 'Grant Permission'}
            </Button>
          </Alert>
        )}

        {/* Permission Denied */}
        {permissions.denied && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Camera permission was denied. Please enable camera access in your browser settings and refresh the page.
            </Typography>
          </Alert>
        )}

        {/* Camera Interface */}
        {permissions.granted && (
          <>
            {/* Camera Preview */}
            <Box mb={2}>
              {isCameraActive ? (
                <Paper
                  elevation={2}
                  sx={{
                    position: 'relative',
                    borderRadius: 2,
                    overflow: 'hidden',
                    aspectRatio: '16/9',
                    bgcolor: 'black',
                  }}
                >
                  <video
                    ref={videoRef}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                    autoPlay
                    muted
                    playsInline
                  />
                  
                  {/* Camera Controls Overlay */}
                  <Box
                    position="absolute"
                    top={8}
                    right={8}
                    display="flex"
                    gap={1}
                  >
                    <IconButton
                      size="small"
                      onClick={handleFacingModeToggle}
                      sx={{
                        bgcolor: 'rgba(0,0,0,0.5)',
                        color: 'white',
                        '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' },
                      }}
                    >
                      <FlipCameraAndroidRounded />
                    </IconButton>
                  </Box>
                </Paper>
              ) : (
                <Paper
                  elevation={1}
                  sx={{
                    aspectRatio: '16/9',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'grey.100',
                    borderRadius: 2,
                  }}
                >
                  <Box textAlign="center">
                    <CameraAltRounded sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      Camera preview will appear here
                    </Typography>
                  </Box>
                </Paper>
              )}
            </Box>

            {/* Control Buttons */}
            <Box display="flex" justifyContent="center" gap={2} mb={2}>
              {!isCameraActive ? (
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<PhotoCameraRounded />}
                  onClick={handleStartCamera}
                  color="primary"
                >
                  Start Camera
                </Button>
              ) : (
                <>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<PhotoCameraRounded />}
                    onClick={handleCapture}
                    color="primary"
                  >
                    Capture
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={stopCamera}
                    color="error"
                  >
                    Stop Camera
                  </Button>
                </>
              )}
            </Box>

            {/* File Upload Option */}
            <Box textAlign="center">
              <Typography variant="body2" color="text.secondary" mb={1}>
                Or upload an image file
              </Typography>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />
              <Button
                variant="outlined"
                startIcon={<UploadFileRounded />}
                onClick={() => fileInputRef.current?.click()}
              >
                Upload Image
              </Button>
            </Box>
          </>
        )}

        {/* Settings Dialog */}
        <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Capture Settings</DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" gap={3} pt={1}>
              {/* Camera Device Selection */}
              <FormControl fullWidth>
                <InputLabel>Camera</InputLabel>
                <Select
                  value={selectedDevice}
                  onChange={(e) => handleDeviceChange(e.target.value)}
                  label="Camera"
                >
                  {cameraDevices.map((device) => (
                    <MenuItem key={device.deviceId} value={device.deviceId}>
                      {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Image Format */}
              <FormControl fullWidth>
                <InputLabel>Format</InputLabel>
                <Select
                  value={captureConfig.format}
                  onChange={(e) => setCaptureConfig(prev => ({ ...prev, format: e.target.value as any }))}
                  label="Format"
                >
                  <MenuItem value="jpeg">JPEG</MenuItem>
                  <MenuItem value="png">PNG</MenuItem>
                  <MenuItem value="webp">WebP</MenuItem>
                </Select>
              </FormControl>

              {/* Image Quality */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Image Quality: {Math.round((captureConfig.quality || 0.8) * 100)}%
                </Typography>
                <Slider
                  value={captureConfig.quality || 0.8}
                  onChange={(_, value) => setCaptureConfig(prev => ({ ...prev, quality: value as number }))}
                  min={0.1}
                  max={1}
                  step={0.1}
                  marks
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                />
              </Box>

              {/* Max Resolution */}
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Max Width: {captureConfig.maxWidth}px
                </Typography>
                <Slider
                  value={captureConfig.maxWidth || 1920}
                  onChange={(_, value) => setCaptureConfig(prev => ({ ...prev, maxWidth: value as number }))}
                  min={640}
                  max={4096}
                  step={160}
                  marks={[
                    { value: 640, label: '640' },
                    { value: 1280, label: '1280' },
                    { value: 1920, label: '1920' },
                    { value: 4096, label: '4K' },
                  ]}
                  valueLabelDisplay="auto"
                />
              </Box>

              {/* Compression Options */}
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={captureConfig.enableCompression}
                      onChange={(e) => setCaptureConfig(prev => ({ ...prev, enableCompression: e.target.checked }))}
                    />
                  }
                  label="Enable Compression"
                />
                {captureConfig.enableCompression && (
                  <Box mt={2}>
                    <Typography variant="body2" gutterBottom>
                      Compression Quality: {Math.round((captureConfig.compressionQuality || 0.8) * 100)}%
                    </Typography>
                    <Slider
                      value={captureConfig.compressionQuality || 0.8}
                      onChange={(_, value) => setCaptureConfig(prev => ({ ...prev, compressionQuality: value as number }))}
                      min={0.1}
                      max={1}
                      step={0.1}
                      valueLabelDisplay="auto"
                      valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                    />
                  </Box>
                )}
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSettingsOpen(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Image Preview Dialog */}
        <Dialog
          open={previewOpen}
          onClose={handleClosePreview}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Typography variant="h6">Captured Image</Typography>
              <IconButton onClick={handleClosePreview}>
                <CloseRounded />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            {capturedImage && (
              <Box textAlign="center">
                <img
                  src={capturedImage}
                  alt="Captured"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '60vh',
                    objectFit: 'contain',
                    borderRadius: 8,
                  }}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClosePreview}>
              Close
            </Button>
            <Button variant="contained" startIcon={<CropRounded />}>
              Edit Image
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};
