import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';
import type { User } from './authApi';

export interface UserProfile extends User {
  preferences: Record<string, any>;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  timezone?: string;
  preferences?: Record<string, any>;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UserStats {
  captures: Array<{
    type: string;
    count: number;
    processed_count: number;
  }>;
  goals: Array<{
    status: string;
    count: number;
  }>;
  tasks: Array<{
    status: string;
    priority: string;
    count: number;
    avg_complexity: number;
  }>;
  recentActivity: Array<{
    activity_type: string;
    activity_subtype: string;
    created_at: string;
  }>;
}

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
  notifications?: {
    email?: boolean;
    push?: boolean;
    taskReminders?: boolean;
    goalMilestones?: boolean;
  };
  dashboard?: {
    layout?: string;
    widgets?: string[];
  };
  capture?: {
    autoProcess?: boolean;
    defaultTags?: string[];
    autoSave?: boolean;
  };
}

export interface DeleteAccountRequest {
  password: string;
  confirmation: 'DELETE_MY_ACCOUNT';
}

export const usersApi = createApi({
  reducerPath: 'usersApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
    credentials: 'include',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['User', 'UserStats', 'UserPreferences'],
  endpoints: (builder) => ({
    getUserProfile: builder.query<{ user: UserProfile }, void>({
      query: () => '/users/profile',
      providesTags: ['User'],
    }),
    
    updateUserProfile: builder.mutation<{ message: string; user: UserProfile }, UpdateProfileRequest>({
      query: (data) => ({
        url: '/users/profile',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),
    
    changePassword: builder.mutation<{ message: string }, ChangePasswordRequest>({
      query: (data) => ({
        url: '/users/change-password',
        method: 'PUT',
        body: data,
      }),
    }),
    
    getUserStats: builder.query<{ stats: UserStats }, void>({
      query: () => '/users/stats',
      providesTags: ['UserStats'],
    }),
    
    getUserPreferences: builder.query<{ preferences: UserPreferences }, void>({
      query: () => '/users/preferences',
      providesTags: ['UserPreferences'],
    }),
    
    updateUserPreferences: builder.mutation<{ message: string; preferences: UserPreferences }, UserPreferences>({
      query: (data) => ({
        url: '/users/preferences',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['UserPreferences'],
    }),
    
    deleteAccount: builder.mutation<{ message: string }, DeleteAccountRequest>({
      query: (data) => ({
        url: '/users/delete-account',
        method: 'DELETE',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),
  }),
});

export const {
  useGetUserProfileQuery,
  useUpdateUserProfileMutation,
  useChangePasswordMutation,
  useGetUserStatsQuery,
  useGetUserPreferencesQuery,
  useUpdateUserPreferencesMutation,
  useDeleteAccountMutation,
} = usersApi;
