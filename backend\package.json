{"name": "mindsync-backend", "version": "1.0.0", "description": "MindSync Backend - Express.js API server for productivity management", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "lint": "eslint src --ext .js,.ts", "lint:fix": "eslint src --ext .js,.ts --fix", "format": "prettier --write src/**/*.{js,ts,json}", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:reset": "npx prisma migrate reset", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio"}, "dependencies": {"axios": "^1.3.4", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^6.14.3", "googleapis": "^118.0.0", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "neo4j-driver": "^5.5.0", "node-cron": "^3.0.2", "nodemailer": "^6.9.1", "pg": "^8.10.0", "redis": "^4.6.5", "sharp": "^0.32.0", "socket.io": "^4.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2", "tesseract.js": "^4.0.2", "winston": "^3.8.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.4.0", "@types/jsonwebtoken": "^9.0.1", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^18.14.6", "@types/node-cron": "^3.0.7", "@types/nodemailer": "^6.4.7", "@types/pg": "^8.6.6", "@types/sharp": "^0.32.0", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^8.35.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.4.3", "nodemon": "^2.0.20", "prettier": "^2.8.4", "prisma": "^4.11.0", "supertest": "^6.3.3", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.9.5"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}