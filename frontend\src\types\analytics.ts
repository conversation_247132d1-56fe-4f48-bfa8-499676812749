/**
 * Analytics and Reporting Types for MindSync
 * Comprehensive interfaces for productivity metrics, data visualization, and reporting
 */

// ============================================================================
// DATA COLLECTION TYPES
// ============================================================================

export interface DataPoint {
  /** Timestamp of the data point */
  timestamp: Date;
  /** Metric value */
  value: number;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

export interface MetricDefinition {
  /** Unique metric identifier */
  id: string;
  /** Human-readable metric name */
  name: string;
  /** Metric description */
  description: string;
  /** Metric category */
  category: 'productivity' | 'goals' | 'tasks' | 'time' | 'engagement' | 'custom';
  /** Data type */
  dataType: 'number' | 'percentage' | 'duration' | 'count' | 'boolean';
  /** Unit of measurement */
  unit: string;
  /** Aggregation method */
  aggregation: 'sum' | 'average' | 'min' | 'max' | 'count' | 'last';
  /** Whether higher values are better */
  higherIsBetter: boolean;
  /** Target value (if applicable) */
  target?: number;
  /** Collection frequency */
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'monthly';
}

export interface DataCollectionEvent {
  /** Event ID */
  id: string;
  /** User ID */
  userId: string;
  /** Event type */
  eventType: string;
  /** Event timestamp */
  timestamp: Date;
  /** Event data */
  data: Record<string, any>;
  /** Session ID */
  sessionId?: string;
  /** Source of the event */
  source: 'user_action' | 'system' | 'integration' | 'scheduled';
}

// ============================================================================
// VISUALIZATION TYPES
// ============================================================================

export interface ChartConfiguration {
  /** Chart type */
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'heatmap' | 'gauge' | 'funnel';
  /** Chart title */
  title: string;
  /** Chart subtitle */
  subtitle?: string;
  /** Data series */
  series: ChartSeries[];
  /** X-axis configuration */
  xAxis: AxisConfiguration;
  /** Y-axis configuration */
  yAxis: AxisConfiguration;
  /** Chart colors */
  colors: string[];
  /** Chart options */
  options: ChartOptions;
}

export interface ChartSeries {
  /** Series name */
  name: string;
  /** Series data */
  data: DataPoint[];
  /** Series color */
  color?: string;
  /** Series type (for mixed charts) */
  type?: string;
  /** Whether to show in legend */
  showInLegend: boolean;
}

export interface AxisConfiguration {
  /** Axis title */
  title: string;
  /** Axis type */
  type: 'linear' | 'logarithmic' | 'datetime' | 'category';
  /** Minimum value */
  min?: number;
  /** Maximum value */
  max?: number;
  /** Tick interval */
  tickInterval?: number;
  /** Number format */
  format?: string;
  /** Whether to show grid lines */
  showGrid: boolean;
}

export interface ChartOptions {
  /** Whether chart is responsive */
  responsive: boolean;
  /** Animation settings */
  animation: {
    enabled: boolean;
    duration: number;
    easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  };
  /** Legend settings */
  legend: {
    enabled: boolean;
    position: 'top' | 'bottom' | 'left' | 'right';
  };
  /** Tooltip settings */
  tooltip: {
    enabled: boolean;
    format?: string;
  };
  /** Export options */
  export: {
    enabled: boolean;
    formats: ('png' | 'jpg' | 'pdf' | 'svg')[];
  };
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardWidget {
  /** Widget ID */
  id: string;
  /** Widget title */
  title: string;
  /** Widget type */
  type: 'chart' | 'metric' | 'table' | 'text' | 'progress' | 'list' | 'calendar';
  /** Widget size */
  size: 'small' | 'medium' | 'large' | 'full';
  /** Widget position */
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  /** Widget configuration */
  configuration: WidgetConfiguration;
  /** Data source */
  dataSource: DataSourceConfiguration;
  /** Refresh interval in seconds */
  refreshInterval: number;
  /** Whether widget is visible */
  visible: boolean;
}

export interface WidgetConfiguration {
  /** Chart configuration (for chart widgets) */
  chart?: ChartConfiguration;
  /** Metric configuration (for metric widgets) */
  metric?: MetricWidgetConfiguration;
  /** Table configuration (for table widgets) */
  table?: TableWidgetConfiguration;
  /** Progress configuration (for progress widgets) */
  progress?: ProgressWidgetConfiguration;
  /** Custom configuration */
  custom?: Record<string, any>;
}

export interface MetricWidgetConfiguration {
  /** Metric ID */
  metricId: string;
  /** Display format */
  format: 'number' | 'percentage' | 'currency' | 'duration';
  /** Show trend indicator */
  showTrend: boolean;
  /** Show comparison to previous period */
  showComparison: boolean;
  /** Comparison period */
  comparisonPeriod: 'previous_period' | 'previous_year' | 'target';
  /** Threshold values for color coding */
  thresholds: {
    good: number;
    warning: number;
    critical: number;
  };
}

export interface TableWidgetConfiguration {
  /** Table columns */
  columns: TableColumn[];
  /** Rows per page */
  pageSize: number;
  /** Whether to show pagination */
  showPagination: boolean;
  /** Whether to allow sorting */
  allowSorting: boolean;
  /** Whether to allow filtering */
  allowFiltering: boolean;
}

export interface TableColumn {
  /** Column key */
  key: string;
  /** Column title */
  title: string;
  /** Column type */
  type: 'text' | 'number' | 'date' | 'boolean' | 'link' | 'badge';
  /** Column width */
  width?: number;
  /** Whether column is sortable */
  sortable: boolean;
  /** Whether column is filterable */
  filterable: boolean;
  /** Column format */
  format?: string;
}

export interface ProgressWidgetConfiguration {
  /** Progress type */
  type: 'linear' | 'circular' | 'gauge';
  /** Current value */
  value: number;
  /** Maximum value */
  max: number;
  /** Show percentage */
  showPercentage: boolean;
  /** Show value */
  showValue: boolean;
  /** Color scheme */
  colorScheme: 'default' | 'success' | 'warning' | 'error' | 'gradient';
}

export interface DataSourceConfiguration {
  /** Data source type */
  type: 'api' | 'static' | 'calculated' | 'realtime';
  /** API endpoint (for API sources) */
  endpoint?: string;
  /** Query parameters */
  parameters?: Record<string, any>;
  /** Data transformation */
  transformation?: DataTransformation;
  /** Cache settings */
  cache?: {
    enabled: boolean;
    duration: number; // seconds
  };
}

export interface DataTransformation {
  /** Transformation type */
  type: 'filter' | 'aggregate' | 'sort' | 'group' | 'calculate' | 'format';
  /** Transformation configuration */
  configuration: Record<string, any>;
}

export interface Dashboard {
  /** Dashboard ID */
  id: string;
  /** Dashboard name */
  name: string;
  /** Dashboard description */
  description?: string;
  /** Dashboard owner */
  userId: string;
  /** Dashboard widgets */
  widgets: DashboardWidget[];
  /** Dashboard layout */
  layout: DashboardLayout;
  /** Dashboard settings */
  settings: DashboardSettings;
  /** Whether dashboard is public */
  isPublic: boolean;
  /** Dashboard tags */
  tags: string[];
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

export interface DashboardLayout {
  /** Layout type */
  type: 'grid' | 'freeform' | 'template';
  /** Grid configuration */
  grid?: {
    columns: number;
    rows: number;
    gap: number;
  };
  /** Responsive breakpoints */
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

export interface DashboardSettings {
  /** Auto-refresh interval */
  autoRefresh: {
    enabled: boolean;
    interval: number; // seconds
  };
  /** Theme settings */
  theme: {
    mode: 'light' | 'dark' | 'auto';
    primaryColor: string;
    backgroundColor: string;
  };
  /** Export settings */
  export: {
    enabled: boolean;
    formats: ('pdf' | 'png' | 'csv' | 'json')[];
    includeData: boolean;
  };
  /** Sharing settings */
  sharing: {
    enabled: boolean;
    allowPublicAccess: boolean;
    allowEmbedding: boolean;
  };
}

// ============================================================================
// REPORT TYPES
// ============================================================================

export interface Report {
  /** Report ID */
  id: string;
  /** Report name */
  name: string;
  /** Report description */
  description?: string;
  /** Report type */
  type: 'productivity' | 'goals' | 'tasks' | 'time_tracking' | 'custom';
  /** Report owner */
  userId: string;
  /** Report configuration */
  configuration: ReportConfiguration;
  /** Report schedule */
  schedule?: ReportSchedule;
  /** Report recipients */
  recipients: ReportRecipient[];
  /** Report status */
  status: 'draft' | 'active' | 'paused' | 'archived';
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

export interface ReportConfiguration {
  /** Time period */
  period: {
    type: 'fixed' | 'relative';
    startDate?: Date;
    endDate?: Date;
    relativePeriod?: 'last_7_days' | 'last_30_days' | 'last_quarter' | 'last_year' | 'custom';
  };
  /** Metrics to include */
  metrics: string[];
  /** Filters to apply */
  filters: ReportFilter[];
  /** Grouping options */
  groupBy: string[];
  /** Sorting options */
  sortBy: {
    field: string;
    direction: 'asc' | 'desc';
  }[];
  /** Output format */
  format: 'pdf' | 'excel' | 'csv' | 'json' | 'html';
  /** Include charts */
  includeCharts: boolean;
  /** Include raw data */
  includeRawData: boolean;
}

export interface ReportFilter {
  /** Filter field */
  field: string;
  /** Filter operator */
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between';
  /** Filter value */
  value: any;
}

export interface ReportSchedule {
  /** Schedule type */
  type: 'once' | 'recurring';
  /** Execution time */
  executeAt?: Date;
  /** Recurrence pattern */
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    interval: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
    time: string; // HH:MM format
  };
  /** Timezone */
  timezone: string;
  /** Next execution time */
  nextExecution?: Date;
}

export interface ReportRecipient {
  /** Recipient type */
  type: 'email' | 'webhook' | 'slack' | 'teams';
  /** Recipient address */
  address: string;
  /** Delivery format */
  format: 'attachment' | 'inline' | 'link';
  /** Whether recipient is active */
  active: boolean;
}

export interface ReportExecution {
  /** Execution ID */
  id: string;
  /** Report ID */
  reportId: string;
  /** Execution status */
  status: 'pending' | 'running' | 'completed' | 'failed';
  /** Start time */
  startedAt: Date;
  /** Completion time */
  completedAt?: Date;
  /** Execution duration */
  duration?: number;
  /** Generated file path */
  filePath?: string;
  /** File size */
  fileSize?: number;
  /** Error message */
  error?: string;
  /** Execution metadata */
  metadata: Record<string, any>;
}

// ============================================================================
// ANALYTICS AGGREGATION TYPES
// ============================================================================

export interface AnalyticsQuery {
  /** Query ID */
  id: string;
  /** Metrics to retrieve */
  metrics: string[];
  /** Time range */
  timeRange: {
    start: Date;
    end: Date;
  };
  /** Filters */
  filters: QueryFilter[];
  /** Grouping */
  groupBy: string[];
  /** Aggregation functions */
  aggregations: QueryAggregation[];
  /** Sorting */
  orderBy: {
    field: string;
    direction: 'asc' | 'desc';
  }[];
  /** Result limit */
  limit?: number;
  /** Result offset */
  offset?: number;
}

export interface QueryFilter {
  /** Filter field */
  field: string;
  /** Filter operator */
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'regex';
  /** Filter value */
  value: any;
}

export interface QueryAggregation {
  /** Aggregation function */
  function: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'distinct' | 'percentile';
  /** Field to aggregate */
  field: string;
  /** Aggregation alias */
  alias?: string;
  /** Aggregation parameters */
  parameters?: Record<string, any>;
}

export interface AnalyticsResult {
  /** Query ID */
  queryId: string;
  /** Result data */
  data: Record<string, any>[];
  /** Result metadata */
  metadata: {
    totalRows: number;
    executionTime: number;
    cacheHit: boolean;
    dataFreshness: Date;
  };
  /** Result columns */
  columns: ResultColumn[];
}

export interface ResultColumn {
  /** Column name */
  name: string;
  /** Column type */
  type: 'string' | 'number' | 'date' | 'boolean';
  /** Column format */
  format?: string;
  /** Column description */
  description?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface AnalyticsApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: Date;
    processingTime: number;
    cacheInfo?: {
      hit: boolean;
      ttl: number;
    };
  };
}

export interface DashboardResponse extends AnalyticsApiResponse<Dashboard> {}
export interface ReportResponse extends AnalyticsApiResponse<Report> {}
export interface AnalyticsQueryResponse extends AnalyticsApiResponse<AnalyticsResult> {}
export interface MetricsResponse extends AnalyticsApiResponse<MetricDefinition[]> {}
