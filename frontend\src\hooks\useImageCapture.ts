import { useState, useRef, useCallback, useEffect } from 'react';
import type { 
  ImageCaptureConfig, 
  ImageCaptureResult,
  ImageMetadata 
} from '@/types/media';

/**
 * Custom hook for image capture functionality
 * TODO: Implement actual camera integration, image processing, and cloud upload
 */

interface UseImageCaptureOptions {
  config?: Partial<ImageCaptureConfig>;
  onImageCaptured?: (result: ImageCaptureResult) => void;
  onError?: (error: string) => void;
  autoUpload?: boolean;
}

interface UseImageCaptureReturn {
  // State
  isSupported: boolean;
  isCameraActive: boolean;
  permissions: {
    granted: boolean;
    denied: boolean;
    requesting: boolean;
  };
  
  // Actions
  startCamera: (facingMode?: 'user' | 'environment') => Promise<void>;
  stopCamera: () => void;
  captureImage: () => Promise<ImageCaptureResult | null>;
  uploadFromFile: (file: File) => Promise<ImageCaptureResult | null>;
  
  // Utilities
  requestPermissions: () => Promise<boolean>;
  getCameraDevices: () => Promise<MediaDeviceInfo[]>;
  setCameraDevice: (deviceId: string) => void;
  
  // Refs for video element
  videoRef: React.RefObject<HTMLVideoElement>;
}

const DEFAULT_CONFIG: ImageCaptureConfig = {
  quality: 0.8,
  maxWidth: 1920,
  maxHeight: 1080,
  format: 'jpeg',
  enableCompression: true,
  compressionQuality: 0.8,
  preserveExif: false,
};

export const useImageCapture = (options: UseImageCaptureOptions = {}): UseImageCaptureReturn => {
  const { config = {}, onImageCaptured, onError, autoUpload = false } = options;
  
  // Merge config with defaults
  const captureConfig = { ...DEFAULT_CONFIG, ...config };
  
  // State
  const [isSupported, setIsSupported] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [permissions, setPermissions] = useState({
    granted: false,
    denied: false,
    requesting: false,
  });
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>('');
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  // Check browser support
  useEffect(() => {
    const checkSupport = () => {
      const hasGetUserMedia = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
      const hasCanvas = typeof HTMLCanvasElement !== 'undefined';
      setIsSupported(hasGetUserMedia && hasCanvas);
    };
    
    checkSupport();
  }, []);

  // Request camera permissions
  const requestPermissions = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      onError?.('Camera is not supported in this browser');
      return false;
    }

    setPermissions(prev => ({ ...prev, requesting: true }));

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: {
          deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined,
          width: { ideal: captureConfig.maxWidth },
          height: { ideal: captureConfig.maxHeight },
        } 
      });
      
      // Stop the stream immediately - we just needed to check permissions
      stream.getTracks().forEach(track => track.stop());
      
      setPermissions({ granted: true, denied: false, requesting: false });
      return true;
    } catch (error) {
      console.error('Camera permission denied:', error);
      setPermissions({ granted: false, denied: true, requesting: false });
      onError?.('Camera permission denied');
      return false;
    }
  }, [isSupported, selectedDeviceId, captureConfig.maxWidth, captureConfig.maxHeight, onError]);

  // Get available camera devices
  const getCameraDevices = useCallback(async (): Promise<MediaDeviceInfo[]> => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'videoinput');
    } catch (error) {
      console.error('Error getting camera devices:', error);
      return [];
    }
  }, []);

  // Set camera device
  const setCameraDevice = useCallback((deviceId: string) => {
    setSelectedDeviceId(deviceId);
  }, []);

  // Start camera
  const startCamera = useCallback(async (facingMode: 'user' | 'environment' = 'environment'): Promise<void> => {
    try {
      if (!permissions.granted) {
        const granted = await requestPermissions();
        if (!granted) return;
      }

      // Stop existing stream if any
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
      }

      // Get media stream
      const constraints: MediaStreamConstraints = {
        video: {
          deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined,
          facingMode: selectedDeviceId ? undefined : { ideal: facingMode },
          width: { ideal: captureConfig.maxWidth },
          height: { ideal: captureConfig.maxHeight },
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      mediaStreamRef.current = stream;

      // Set video source
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }

      setIsCameraActive(true);

    } catch (error) {
      console.error('Error starting camera:', error);
      onError?.('Failed to start camera');
      setIsCameraActive(false);
    }
  }, [permissions.granted, requestPermissions, selectedDeviceId, captureConfig.maxWidth, captureConfig.maxHeight, onError]);

  // Stop camera
  const stopCamera = useCallback(() => {
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setIsCameraActive(false);
  }, []);

  // Capture image from camera
  const captureImage = useCallback(async (): Promise<ImageCaptureResult | null> => {
    try {
      if (!videoRef.current || !isCameraActive) {
        onError?.('Camera is not active');
        return null;
      }

      // Create canvas if not exists
      if (!canvasRef.current) {
        canvasRef.current = document.createElement('canvas');
      }

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        onError?.('Failed to get canvas context');
        return null;
      }

      // Set canvas dimensions
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to blob
      const imageBlob = await new Promise<Blob | null>((resolve) => {
        canvas.toBlob(
          resolve,
          `image/${captureConfig.format}`,
          captureConfig.quality
        );
      });

      if (!imageBlob) {
        onError?.('Failed to create image blob');
        return null;
      }

      // Create metadata
      const metadata: ImageMetadata = {
        width: canvas.width,
        height: canvas.height,
        fileSize: imageBlob.size,
        format: captureConfig.format,
        capturedAt: new Date(),
        deviceInfo: {
          deviceId: selectedDeviceId || 'default',
          label: 'Default camera', // TODO: Get actual device label
          facingMode: 'environment', // TODO: Detect actual facing mode
        },
        // TODO: Add EXIF data and color analysis
      };

      // Create result
      const result: ImageCaptureResult = {
        id: crypto.randomUUID(),
        imageBlob,
        metadata,
        processingStatus: 'pending',
      };

      // TODO: Auto-upload if enabled
      if (autoUpload) {
        // Implement upload logic
      }

      onImageCaptured?.(result);
      return result;

    } catch (error) {
      console.error('Error capturing image:', error);
      onError?.('Failed to capture image');
      return null;
    }
  }, [isCameraActive, captureConfig.format, captureConfig.quality, selectedDeviceId, autoUpload, onImageCaptured, onError]);

  // Upload image from file
  const uploadFromFile = useCallback(async (file: File): Promise<ImageCaptureResult | null> => {
    try {
      // Validate file
      if (!file.type.startsWith('image/')) {
        onError?.('Selected file is not an image');
        return null;
      }

      // Check file size (optional)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        onError?.('Image file is too large (max 10MB)');
        return null;
      }

      // Process image if needed
      let processedBlob: Blob = file;
      let width = 0;
      let height = 0;

      // Get image dimensions and optionally resize
      if (captureConfig.enableCompression || captureConfig.maxWidth || captureConfig.maxHeight) {
        const processedResult = await processImageFile(file, captureConfig);
        processedBlob = processedResult.blob;
        width = processedResult.width;
        height = processedResult.height;
      } else {
        // Get dimensions without processing
        const dimensions = await getImageDimensions(file);
        width = dimensions.width;
        height = dimensions.height;
      }

      // Create metadata
      const metadata: ImageMetadata = {
        width,
        height,
        fileSize: processedBlob.size,
        format: captureConfig.format,
        capturedAt: new Date(),
        // TODO: Extract EXIF data if preserveExif is true
      };

      // Create result
      const result: ImageCaptureResult = {
        id: crypto.randomUUID(),
        imageBlob: processedBlob,
        metadata,
        processingStatus: 'pending',
      };

      // TODO: Auto-upload if enabled
      if (autoUpload) {
        // Implement upload logic
      }

      onImageCaptured?.(result);
      return result;

    } catch (error) {
      console.error('Error processing uploaded file:', error);
      onError?.('Failed to process uploaded image');
      return null;
    }
  }, [captureConfig, autoUpload, onImageCaptured, onError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return {
    isSupported,
    isCameraActive,
    permissions,
    startCamera,
    stopCamera,
    captureImage,
    uploadFromFile,
    requestPermissions,
    getCameraDevices,
    setCameraDevice,
    videoRef,
  };
};

// Helper functions
async function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

async function processImageFile(
  file: File, 
  config: ImageCaptureConfig
): Promise<{ blob: Blob; width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      // Calculate new dimensions
      let { width, height } = img;
      
      if (config.maxWidth && width > config.maxWidth) {
        height = (height * config.maxWidth) / width;
        width = config.maxWidth;
      }
      
      if (config.maxHeight && height > config.maxHeight) {
        width = (width * config.maxHeight) / height;
        height = config.maxHeight;
      }

      // Set canvas size
      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve({ blob, width, height });
          } else {
            reject(new Error('Failed to create compressed image'));
          }
        },
        `image/${config.format}`,
        config.enableCompression ? config.compressionQuality : 1
      );
    };
    
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}
