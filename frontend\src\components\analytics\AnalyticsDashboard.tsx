import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Switch,
  FormControlLabel,
  Chip,
  Alert,
} from '@mui/material';
import {
  DashboardRounded,
  AddRounded,
  EditRounded,
  DeleteRounded,
  RefreshRounded,
  SettingsRounded,
  ShareRounded,
  DownloadRounded,
  FullscreenRounded,
  GridViewRounded,
} from '@mui/icons-material';

import type { Dashboard, DashboardWidget } from '@/types/analytics';
import { useNotification } from '@/components/NotificationProvider';
import { MetricWidget } from './MetricWidget';
import { ChartWidget } from './ChartWidget';
import { TableWidget } from './TableWidget';
import { ProgressWidget } from './ProgressWidget';

/**
 * Analytics Dashboard Component
 * TODO: Implement drag-and-drop widget arrangement, real-time updates, and export functionality
 */

interface AnalyticsDashboardProps {
  dashboardId?: string;
  onDashboardChange?: (dashboard: Dashboard) => void;
  className?: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  dashboardId,
  onDashboardChange,
  className,
}) => {
  const { showSuccess, showError } = useNotification();
  
  // State
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [addWidgetOpen, setAddWidgetOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<DashboardWidget | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Load dashboard on mount
  useEffect(() => {
    if (dashboardId) {
      loadDashboard(dashboardId);
    } else {
      createDefaultDashboard();
    }
  }, [dashboardId]);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh || !dashboard) return;

    const interval = setInterval(() => {
      refreshDashboard();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, dashboard]);

  // Load dashboard
  const loadDashboard = async (id: string) => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      const mockDashboard = await generateMockDashboard(id);
      setDashboard(mockDashboard);
      
    } catch (error) {
      console.error('Error loading dashboard:', error);
      showError('Failed to load dashboard');
    } finally {
      setLoading(false);
    }
  };

  // Create default dashboard
  const createDefaultDashboard = async () => {
    try {
      setLoading(true);
      
      const defaultDashboard = await generateMockDashboard('default');
      setDashboard(defaultDashboard);
      
    } catch (error) {
      console.error('Error creating default dashboard:', error);
      showError('Failed to create dashboard');
    } finally {
      setLoading(false);
    }
  };

  // Refresh dashboard data
  const refreshDashboard = async () => {
    if (!dashboard) return;
    
    try {
      // TODO: Implement actual data refresh
      showSuccess('Dashboard refreshed');
    } catch (error) {
      showError('Failed to refresh dashboard');
    }
  };

  // Handle widget add
  const handleAddWidget = async (widgetType: string) => {
    if (!dashboard) return;

    try {
      const newWidget: DashboardWidget = {
        id: `widget-${Date.now()}`,
        title: `New ${widgetType} Widget`,
        type: widgetType as any,
        size: 'medium',
        position: { x: 0, y: 0, width: 6, height: 4 },
        configuration: getDefaultWidgetConfiguration(widgetType),
        dataSource: { type: 'api', endpoint: '/api/analytics/metrics' },
        refreshInterval: 60,
        visible: true,
      };

      const updatedDashboard = {
        ...dashboard,
        widgets: [...dashboard.widgets, newWidget],
        updatedAt: new Date(),
      };

      setDashboard(updatedDashboard);
      setAddWidgetOpen(false);
      onDashboardChange?.(updatedDashboard);
      
      showSuccess('Widget added successfully');
      
    } catch (error) {
      showError('Failed to add widget');
    }
  };

  // Handle widget update
  const handleWidgetUpdate = (widgetId: string, updates: Partial<DashboardWidget>) => {
    if (!dashboard) return;

    const updatedWidgets = dashboard.widgets.map(widget =>
      widget.id === widgetId ? { ...widget, ...updates } : widget
    );

    const updatedDashboard = {
      ...dashboard,
      widgets: updatedWidgets,
      updatedAt: new Date(),
    };

    setDashboard(updatedDashboard);
    onDashboardChange?.(updatedDashboard);
  };

  // Handle widget delete
  const handleWidgetDelete = (widgetId: string) => {
    if (!dashboard) return;

    const updatedWidgets = dashboard.widgets.filter(widget => widget.id !== widgetId);
    
    const updatedDashboard = {
      ...dashboard,
      widgets: updatedWidgets,
      updatedAt: new Date(),
    };

    setDashboard(updatedDashboard);
    onDashboardChange?.(updatedDashboard);
    
    showSuccess('Widget deleted');
  };

  // Get default widget configuration
  const getDefaultWidgetConfiguration = (widgetType: string) => {
    switch (widgetType) {
      case 'metric':
        return {
          metric: {
            metricId: 'tasks_completed',
            format: 'number',
            showTrend: true,
            showComparison: true,
            comparisonPeriod: 'previous_period',
            thresholds: { good: 80, warning: 60, critical: 40 },
          },
        };
      case 'chart':
        return {
          chart: {
            type: 'line',
            title: 'Productivity Trend',
            series: [],
            xAxis: { title: 'Date', type: 'datetime', showGrid: true },
            yAxis: { title: 'Value', type: 'linear', showGrid: true },
            colors: ['#1976d2', '#dc004e', '#9c27b0'],
            options: {
              responsive: true,
              animation: { enabled: true, duration: 300, easing: 'ease' },
              legend: { enabled: true, position: 'bottom' },
              tooltip: { enabled: true },
              export: { enabled: true, formats: ['png', 'pdf'] },
            },
          },
        };
      case 'progress':
        return {
          progress: {
            type: 'circular',
            value: 0,
            max: 100,
            showPercentage: true,
            showValue: true,
            colorScheme: 'default',
          },
        };
      default:
        return {};
    }
  };

  // Render widget based on type
  const renderWidget = (widget: DashboardWidget) => {
    const commonProps = {
      widget,
      editMode,
      onUpdate: (updates: Partial<DashboardWidget>) => handleWidgetUpdate(widget.id, updates),
      onDelete: () => handleWidgetDelete(widget.id),
    };

    switch (widget.type) {
      case 'metric':
        return <MetricWidget key={widget.id} {...commonProps} />;
      case 'chart':
        return <ChartWidget key={widget.id} {...commonProps} />;
      case 'table':
        return <TableWidget key={widget.id} {...commonProps} />;
      case 'progress':
        return <ProgressWidget key={widget.id} {...commonProps} />;
      default:
        return (
          <Card key={widget.id}>
            <CardContent>
              <Typography variant="h6">{widget.title}</Typography>
              <Typography color="text.secondary">
                Widget type "{widget.type}" not implemented
              </Typography>
            </CardContent>
          </Card>
        );
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={400}>
        <Typography>Loading dashboard...</Typography>
      </Box>
    );
  }

  if (!dashboard) {
    return (
      <Box textAlign="center" py={8}>
        <DashboardRounded sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h5" gutterBottom>
          No Dashboard Available
        </Typography>
        <Typography color="text.secondary" mb={3}>
          Create a dashboard to start visualizing your analytics
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddRounded />}
          onClick={createDefaultDashboard}
        >
          Create Dashboard
        </Button>
      </Box>
    );
  }

  return (
    <Box className={className}>
      {/* Dashboard Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box>
          <Typography variant="h4" fontWeight={600} gutterBottom>
            {dashboard.name}
          </Typography>
          {dashboard.description && (
            <Typography variant="body2" color="text.secondary">
              {dashboard.description}
            </Typography>
          )}
          <Box display="flex" gap={1} mt={1}>
            {dashboard.tags.map((tag) => (
              <Chip key={tag} label={tag} size="small" variant="outlined" />
            ))}
          </Box>
        </Box>
        
        <Box display="flex" gap={1}>
          <Tooltip title="Toggle edit mode">
            <IconButton
              onClick={() => setEditMode(!editMode)}
              color={editMode ? 'primary' : 'default'}
            >
              <EditRounded />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Add widget">
            <IconButton onClick={() => setAddWidgetOpen(true)}>
              <AddRounded />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Refresh dashboard">
            <IconButton onClick={refreshDashboard}>
              <RefreshRounded />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Dashboard settings">
            <IconButton onClick={() => setSettingsOpen(true)}>
              <SettingsRounded />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Share dashboard">
            <IconButton>
              <ShareRounded />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Export dashboard">
            <IconButton>
              <DownloadRounded />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Auto-refresh indicator */}
      {autoRefresh && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            Auto-refresh enabled (every {refreshInterval} seconds)
          </Typography>
        </Alert>
      )}

      {/* Dashboard Grid */}
      <Grid container spacing={3}>
        {dashboard.widgets
          .filter(widget => widget.visible)
          .map((widget) => (
            <Grid
              item
              xs={12}
              sm={widget.size === 'small' ? 6 : widget.size === 'medium' ? 6 : 12}
              md={widget.size === 'small' ? 4 : widget.size === 'medium' ? 6 : 12}
              lg={widget.size === 'small' ? 3 : widget.size === 'medium' ? 6 : 12}
              key={widget.id}
            >
              {renderWidget(widget)}
            </Grid>
          ))}
      </Grid>

      {/* Empty state */}
      {dashboard.widgets.length === 0 && (
        <Box textAlign="center" py={8}>
          <GridViewRounded sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No Widgets Added
          </Typography>
          <Typography color="text.secondary" mb={3}>
            Add widgets to start building your analytics dashboard
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddRounded />}
            onClick={() => setAddWidgetOpen(true)}
          >
            Add Widget
          </Button>
        </Box>
      )}

      {/* Add Widget Dialog */}
      <Dialog open={addWidgetOpen} onClose={() => setAddWidgetOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Widget</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <Typography variant="body2" color="text.secondary">
              Choose a widget type to add to your dashboard
            </Typography>
            
            <Grid container spacing={2}>
              {[
                { type: 'metric', label: 'Metric', description: 'Display a single metric value' },
                { type: 'chart', label: 'Chart', description: 'Visualize data with charts' },
                { type: 'table', label: 'Table', description: 'Display data in table format' },
                { type: 'progress', label: 'Progress', description: 'Show progress indicators' },
              ].map((widgetType) => (
                <Grid item xs={6} key={widgetType.type}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      '&:hover': { bgcolor: 'action.hover' },
                    }}
                    onClick={() => handleAddWidget(widgetType.type)}
                  >
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {widgetType.label}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {widgetType.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddWidgetOpen(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dashboard Settings Dialog */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Dashboard Settings</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={3} pt={1}>
            <TextField
              label="Dashboard Name"
              value={dashboard.name}
              onChange={(e) => setDashboard({ ...dashboard, name: e.target.value })}
              fullWidth
            />
            
            <TextField
              label="Description"
              value={dashboard.description || ''}
              onChange={(e) => setDashboard({ ...dashboard, description: e.target.value })}
              multiline
              rows={3}
              fullWidth
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                />
              }
              label="Auto-refresh"
            />
            
            {autoRefresh && (
              <FormControl fullWidth>
                <InputLabel>Refresh Interval</InputLabel>
                <Select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(e.target.value as number)}
                  label="Refresh Interval"
                >
                  <MenuItem value={10}>10 seconds</MenuItem>
                  <MenuItem value={30}>30 seconds</MenuItem>
                  <MenuItem value={60}>1 minute</MenuItem>
                  <MenuItem value={300}>5 minutes</MenuItem>
                  <MenuItem value={900}>15 minutes</MenuItem>
                </Select>
              </FormControl>
            )}
            
            <FormControlLabel
              control={
                <Switch
                  checked={dashboard.isPublic}
                  onChange={(e) => setDashboard({ ...dashboard, isPublic: e.target.checked })}
                />
              }
              label="Make dashboard public"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={() => {
            setSettingsOpen(false);
            onDashboardChange?.(dashboard);
            showSuccess('Dashboard settings saved');
          }}>
            Save Settings
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Mock dashboard generation
async function generateMockDashboard(id: string): Promise<Dashboard> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id,
        name: 'Productivity Dashboard',
        description: 'Track your productivity metrics and goals',
        userId: 'user-123',
        widgets: [
          {
            id: 'widget-1',
            title: 'Tasks Completed',
            type: 'metric',
            size: 'small',
            position: { x: 0, y: 0, width: 3, height: 2 },
            configuration: {
              metric: {
                metricId: 'tasks_completed',
                format: 'number',
                showTrend: true,
                showComparison: true,
                comparisonPeriod: 'previous_period',
                thresholds: { good: 80, warning: 60, critical: 40 },
              },
            },
            dataSource: { type: 'api', endpoint: '/api/analytics/metrics' },
            refreshInterval: 60,
            visible: true,
          },
          {
            id: 'widget-2',
            title: 'Productivity Trend',
            type: 'chart',
            size: 'large',
            position: { x: 3, y: 0, width: 9, height: 4 },
            configuration: {
              chart: {
                type: 'line',
                title: 'Productivity Trend',
                series: [],
                xAxis: { title: 'Date', type: 'datetime', showGrid: true },
                yAxis: { title: 'Score', type: 'linear', showGrid: true },
                colors: ['#1976d2'],
                options: {
                  responsive: true,
                  animation: { enabled: true, duration: 300, easing: 'ease' },
                  legend: { enabled: true, position: 'bottom' },
                  tooltip: { enabled: true },
                  export: { enabled: true, formats: ['png', 'pdf'] },
                },
              },
            },
            dataSource: { type: 'api', endpoint: '/api/analytics/productivity-trend' },
            refreshInterval: 300,
            visible: true,
          },
        ],
        layout: {
          type: 'grid',
          grid: { columns: 12, rows: 8, gap: 16 },
          breakpoints: { mobile: 768, tablet: 1024, desktop: 1200 },
        },
        settings: {
          autoRefresh: { enabled: false, interval: 60 },
          theme: { mode: 'light', primaryColor: '#1976d2', backgroundColor: '#ffffff' },
          export: { enabled: true, formats: ['pdf', 'png'], includeData: true },
          sharing: { enabled: true, allowPublicAccess: false, allowEmbedding: false },
        },
        isPublic: false,
        tags: ['productivity', 'personal'],
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }, 500);
  });
}
