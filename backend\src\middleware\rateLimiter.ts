import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { getRedisClient } from '@/config/database';
import { logger } from '@/utils/logger';
import { RateLimitError } from './errorHandler';

/**
 * Redis store for rate limiting
 */
class RedisStore {
  private client = getRedisClient();
  private prefix = 'rate_limit:';

  async increment(key: string): Promise<{ totalHits: number; timeToExpire?: number }> {
    const redisKey = this.prefix + key;
    
    try {
      const multi = this.client.multi();
      multi.incr(redisKey);
      multi.expire(redisKey, 900); // 15 minutes
      multi.ttl(redisKey);
      
      const results = await multi.exec();
      
      if (!results) {
        throw new Error('Redis multi-exec failed');
      }
      
      const totalHits = results[0] as number;
      const ttl = results[2] as number;
      
      return {
        totalHits,
        timeToExpire: ttl > 0 ? ttl * 1000 : undefined
      };
    } catch (error) {
      logger.error('Redis rate limit error:', error);
      // Fallback to allowing the request if <PERSON><PERSON> fails
      return { totalHits: 1 };
    }
  }

  async decrement(key: string): Promise<void> {
    const redisKey = this.prefix + key;
    
    try {
      await this.client.decr(redisKey);
    } catch (error) {
      logger.error('Redis rate limit decrement error:', error);
    }
  }

  async resetKey(key: string): Promise<void> {
    const redisKey = this.prefix + key;
    
    try {
      await this.client.del(redisKey);
    } catch (error) {
      logger.error('Redis rate limit reset error:', error);
    }
  }
}

/**
 * Custom key generator that includes user ID if available
 */
const keyGenerator = (req: Request): string => {
  const userId = (req as any).user?.id;
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  
  if (userId) {
    return `user:${userId}`;
  }
  
  return `ip:${ip}`;
};

/**
 * Custom handler for rate limit exceeded
 */
const rateLimitHandler = (req: Request, res: Response) => {
  const error = new RateLimitError('Too many requests, please try again later');
  
  logger.warn('Rate limit exceeded', {
    ip: req.ip,
    userId: (req as any).user?.id,
    path: req.path,
    method: req.method,
    userAgent: req.get('User-Agent')
  });
  
  res.status(429).json({
    error: {
      message: error.message,
      code: error.code,
      statusCode: 429,
      timestamp: new Date().toISOString(),
      retryAfter: res.get('Retry-After')
    }
  });
};

/**
 * Skip rate limiting for certain conditions
 */
const skipRateLimit = (req: Request): boolean => {
  // Skip for health checks
  if (req.path === '/health') {
    return true;
  }
  
  // Skip for admin users (if implemented)
  const user = (req as any).user;
  if (user && user.role === 'admin') {
    return true;
  }
  
  // Skip in test environment
  if (process.env.NODE_ENV === 'test') {
    return true;
  }
  
  return false;
};

/**
 * General API rate limiter
 */
export const rateLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 100 requests per window
  message: 'Too many requests from this IP, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator,
  handler: rateLimitHandler,
  skip: skipRateLimit,
  store: new RedisStore() as any
});

/**
 * Strict rate limiter for authentication endpoints
 */
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const email = req.body.email || 'unknown';
    return `auth:${ip}:${email}`;
  },
  handler: rateLimitHandler,
  skip: skipRateLimit,
  store: new RedisStore() as any
});

/**
 * File upload rate limiter
 */
export const uploadRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 uploads per hour
  message: 'Too many file uploads, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator,
  handler: rateLimitHandler,
  skip: skipRateLimit,
  store: new RedisStore() as any
});

/**
 * API key rate limiter (for external integrations)
 */
export const apiKeyRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // 1000 requests per hour for API keys
  message: 'API key rate limit exceeded',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const apiKey = req.headers['x-api-key'] as string;
    return `api_key:${apiKey || 'unknown'}`;
  },
  handler: rateLimitHandler,
  skip: (req: Request) => {
    // Only apply to requests with API keys
    return !req.headers['x-api-key'];
  },
  store: new RedisStore() as any
});

/**
 * Password reset rate limiter
 */
export const passwordResetRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour
  message: 'Too many password reset attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const email = req.body.email || 'unknown';
    return `password_reset:${email}`;
  },
  handler: rateLimitHandler,
  skip: skipRateLimit,
  store: new RedisStore() as any
});

/**
 * Create custom rate limiter
 */
export const createRateLimiter = (options: {
  windowMs: number;
  max: number;
  message?: string;
  keyPrefix?: string;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: options.message || 'Rate limit exceeded',
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req: Request) => {
      const baseKey = keyGenerator(req);
      return options.keyPrefix ? `${options.keyPrefix}:${baseKey}` : baseKey;
    },
    handler: rateLimitHandler,
    skip: skipRateLimit,
    store: new RedisStore() as any
  });
};
