import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Box,
  Tabs,
  Tab,
  IconButton,
  Chip,
  <PERSON><PERSON>,
  <PERSON>lapse,
} from '@mui/material';
import {
  TextFieldsRounded,
  MicRounded,
  PhotoCameraRounded,
  EmailRounded,
  LinkRounded,
  SendRounded,
  ExpandMoreRounded,
  ExpandLessRounded,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

import { useCaptureTextMutation } from '@/store/api/captureApi';
import { useNotification } from '@/components/NotificationProvider';
import { ButtonLoading } from '@/components/LoadingScreen';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`capture-tabpanel-${index}`}
      aria-labelledby={`capture-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
}

const validationSchema = Yup.object({
  content: Yup.string()
    .min(1, 'Content is required')
    .max(10000, 'Content must be less than 10,000 characters')
    .required('Content is required'),
});

export const QuickCaptureWidget: React.FC = () => {
  const { showSuccess, showError } = useNotification();
  const [captureText, { isLoading }] = useCaptureTextMutation();
  
  const [tabValue, setTabValue] = useState(0);
  const [expanded, setExpanded] = useState(false);
  const [processingResult, setProcessingResult] = useState<any>(null);

  const formik = useFormik({
    initialValues: {
      content: '',
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        const result = await captureText(values).unwrap();
        
        setProcessingResult(result.capture);
        showSuccess('Content captured and processed successfully!');
        resetForm();
        setExpanded(true);
      } catch (error: any) {
        const errorMessage = error?.data?.error?.message || 'Failed to capture content';
        showError(errorMessage);
      }
    },
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleExpandToggle = () => {
    setExpanded(!expanded);
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Quick Capture
          </Typography>
          <Chip
            label="AI Powered"
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ mb: 2, borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<TextFieldsRounded />}
            label="Text"
            iconPosition="start"
            sx={{ minHeight: 48 }}
          />
          <Tab
            icon={<MicRounded />}
            label="Voice"
            iconPosition="start"
            sx={{ minHeight: 48 }}
            disabled
          />
          <Tab
            icon={<PhotoCameraRounded />}
            label="Image"
            iconPosition="start"
            sx={{ minHeight: 48 }}
            disabled
          />
          <Tab
            icon={<EmailRounded />}
            label="Email"
            iconPosition="start"
            sx={{ minHeight: 48 }}
            disabled
          />
          <Tab
            icon={<LinkRounded />}
            label="Web Clip"
            iconPosition="start"
            sx={{ minHeight: 48 }}
            disabled
          />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Box component="form" onSubmit={formik.handleSubmit}>
            <TextField
              fullWidth
              multiline
              rows={4}
              name="content"
              placeholder="Capture your thoughts, ideas, tasks, or any content here..."
              value={formik.values.content}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.content && Boolean(formik.errors.content)}
              helperText={formik.touched.content && formik.errors.content}
              sx={{ mb: 2 }}
            />
            
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="caption" color="text.secondary">
                {formik.values.content.length}/10,000 characters
              </Typography>
              
              <Button
                type="submit"
                variant="contained"
                startIcon={<SendRounded />}
                disabled={isLoading || !formik.values.content.trim()}
              >
                <ButtonLoading loading={isLoading}>
                  Capture & Process
                </ButtonLoading>
              </Button>
            </Box>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Alert severity="info">
            Voice capture coming soon! Record audio and automatically transcribe it with AI.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Alert severity="info">
            Image capture coming soon! Upload images and extract text using OCR technology.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Alert severity="info">
            Email processing coming soon! Analyze emails and extract actionable items automatically.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <Alert severity="info">
            Web clipper coming soon! Capture and process content from any webpage.
          </Alert>
        </TabPanel>

        {/* Processing Results */}
        {processingResult && (
          <Box sx={{ mt: 3 }}>
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              sx={{ cursor: 'pointer' }}
              onClick={handleExpandToggle}
            >
              <Typography variant="subtitle1" fontWeight={600}>
                AI Processing Results
              </Typography>
              <IconButton size="small">
                {expanded ? <ExpandLessRounded /> : <ExpandMoreRounded />}
              </IconButton>
            </Box>

            <Collapse in={expanded}>
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
                {/* Extracted Entities */}
                {processingResult.extractedEntities?.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Extracted Entities:
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={1}>
                      {processingResult.extractedEntities.map((entity: any, index: number) => (
                        <Chip
                          key={index}
                          label={`${entity.name} (${entity.type})`}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* Suggested Tags */}
                {processingResult.suggestedTags?.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Suggested Tags:
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={1}>
                      {processingResult.suggestedTags.map((tag: string, index: number) => (
                        <Chip
                          key={index}
                          label={tag}
                          size="small"
                          variant="outlined"
                          color="secondary"
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* Confidence Score */}
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="subtitle2">
                    Processing Confidence:
                  </Typography>
                  <Chip
                    label={`${Math.round(processingResult.confidence * 100)}%`}
                    size="small"
                    color={processingResult.confidence > 0.8 ? 'success' : 'warning'}
                  />
                </Box>
              </Box>
            </Collapse>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
