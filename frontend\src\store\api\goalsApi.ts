import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';

export interface Goal {
  id: string;
  userId: string;
  parentGoalId?: string;
  title: string;
  description?: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  targetValue?: number;
  currentValue: number;
  unit?: string;
  startDate?: string;
  targetDate?: string;
  completedAt?: string;
  priority: number;
  color: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  subgoalCount?: number;
  taskCount?: number;
  completedTasks?: number;
  avgTaskComplexity?: number;
  subgoals?: Goal[];
  recentTasks?: any[];
}

export interface CreateGoalRequest {
  title: string;
  description?: string;
  parentGoalId?: string;
  targetValue?: number;
  unit?: string;
  startDate?: string;
  targetDate?: string;
  priority?: number;
  color?: string;
  metadata?: Record<string, any>;
}

export interface UpdateGoalRequest {
  title?: string;
  description?: string;
  status?: 'active' | 'completed' | 'paused' | 'cancelled';
  targetValue?: number;
  currentValue?: number;
  unit?: string;
  startDate?: string;
  targetDate?: string;
  priority?: number;
  color?: string;
  metadata?: Record<string, any>;
}

export interface GoalsQueryParams {
  status?: 'active' | 'completed' | 'paused' | 'cancelled';
  parentId?: string;
  limit?: number;
  offset?: number;
}

export interface GoalsResponse {
  goals: Goal[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
  };
}

export interface GoalResponse {
  message: string;
  goal: Goal;
}

export interface GoalDetailResponse {
  goal: Goal;
}

export const goalsApi = createApi({
  reducerPath: 'goalsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
    credentials: 'include',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Goal'],
  endpoints: (builder) => ({
    getGoals: builder.query<GoalsResponse, GoalsQueryParams>({
      query: (params) => ({
        url: '/goals',
        params,
      }),
      providesTags: ['Goal'],
    }),
    
    getGoal: builder.query<GoalDetailResponse, string>({
      query: (id) => `/goals/${id}`,
      providesTags: (result, error, id) => [{ type: 'Goal', id }],
    }),
    
    createGoal: builder.mutation<GoalResponse, CreateGoalRequest>({
      query: (data) => ({
        url: '/goals',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Goal'],
    }),
    
    updateGoal: builder.mutation<GoalResponse, { id: string; data: UpdateGoalRequest }>({
      query: ({ id, data }) => ({
        url: `/goals/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Goal', id }, 'Goal'],
    }),
    
    deleteGoal: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/goals/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Goal'],
    }),
  }),
});

export const {
  useGetGoalsQuery,
  useGetGoalQuery,
  useCreateGoalMutation,
  useUpdateGoalMutation,
  useDeleteGoalMutation,
} = goalsApi;
