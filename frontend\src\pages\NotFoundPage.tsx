import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Container,
  Paper,
} from '@mui/material';
import {
  HomeRounded,
  ArrowBackRounded,
  SearchOffRounded,
} from '@mui/icons-material';

export const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="md" sx={{ mt: 8 }}>
      <Paper elevation={3} sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap={3}
        >
          <SearchOffRounded
            sx={{ fontSize: 120, color: 'text.secondary', opacity: 0.5 }}
          />
          
          <Typography variant="h1" fontWeight={700} color="primary">
            404
          </Typography>
          
          <Typography variant="h4" fontWeight={600} gutterBottom>
            Page Not Found
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            The page you're looking for doesn't exist or has been moved.
          </Typography>

          <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
            <Button
              variant="contained"
              startIcon={<HomeRounded />}
              onClick={() => navigate('/dashboard')}
              size="large"
            >
              Go Home
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<ArrowBackRounded />}
              onClick={() => navigate(-1)}
              size="large"
            >
              Go Back
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};
