import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';

// API Slices
import { authApi } from './api/authApi';
import { captureApi } from './api/captureApi';
import { goalsApi } from './api/goalsApi';
import { tasksApi } from './api/tasksApi';
import { usersApi } from './api/usersApi';

// Regular Slices
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import captureSlice from './slices/captureSlice';
import goalsSlice from './slices/goalsSlice';
import tasksSlice from './slices/tasksSlice';

export const store = configureStore({
  reducer: {
    // API reducers
    [authApi.reducerPath]: authApi.reducer,
    [captureApi.reducerPath]: captureApi.reducer,
    [goalsApi.reducerPath]: goalsApi.reducer,
    [tasksApi.reducerPath]: tasksApi.reducer,
    [usersApi.reducerPath]: usersApi.reducer,
    
    // Regular reducers
    auth: authSlice,
    ui: uiSlice,
    capture: captureSlice,
    goals: goalsSlice,
    tasks: tasksSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          // Ignore these action types
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/REGISTER',
        ],
      },
    }).concat(
      authApi.middleware,
      captureApi.middleware,
      goalsApi.middleware,
      tasksApi.middleware,
      usersApi.middleware
    ),
  devTools: process.env.NODE_ENV !== 'production',
});

// Setup listeners for refetchOnFocus/refetchOnReconnect behaviors
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export hooks for typed usage
export { useAppDispatch, useAppSelector } from './hooks';
