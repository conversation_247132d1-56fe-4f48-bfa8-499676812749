/**
 * Media capture and processing types for MindSync
 * Foundational interfaces for voice and image capture functionality
 */

// ============================================================================
// VOICE CAPTURE TYPES
// ============================================================================

export interface VoiceRecordingConfig {
  /** Audio quality settings */
  quality: 'low' | 'medium' | 'high' | 'lossless';
  /** Maximum recording duration in seconds */
  maxDuration: number;
  /** Audio format preference */
  format: 'webm' | 'mp4' | 'wav' | 'ogg';
  /** Sample rate in Hz */
  sampleRate: 16000 | 22050 | 44100 | 48000;
  /** Number of audio channels */
  channels: 1 | 2;
  /** Enable noise reduction */
  noiseReduction: boolean;
  /** Enable echo cancellation */
  echoCancellation: boolean;
  /** Auto gain control */
  autoGainControl: boolean;
}

export interface VoiceRecordingState {
  /** Current recording status */
  status: 'idle' | 'recording' | 'paused' | 'processing' | 'completed' | 'error';
  /** Recording duration in seconds */
  duration: number;
  /** Audio level (0-100) */
  audioLevel: number;
  /** File size in bytes */
  fileSize: number;
  /** Error message if any */
  error?: string;
  /** Recording start timestamp */
  startTime?: Date;
  /** Recording end timestamp */
  endTime?: Date;
}

export interface VoiceRecordingResult {
  /** Unique recording ID */
  id: string;
  /** Audio blob data */
  audioBlob: Blob;
  /** Recording metadata */
  metadata: VoiceRecordingMetadata;
  /** Transcription result (if available) */
  transcription?: TranscriptionResult;
  /** Processing status */
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface VoiceRecordingMetadata {
  /** Recording duration in seconds */
  duration: number;
  /** File size in bytes */
  fileSize: number;
  /** Audio format */
  format: string;
  /** Sample rate */
  sampleRate: number;
  /** Number of channels */
  channels: number;
  /** Recording timestamp */
  recordedAt: Date;
  /** Device information */
  deviceInfo: {
    deviceId: string;
    label: string;
    kind: string;
  };
  /** Quality metrics */
  qualityMetrics: {
    averageLevel: number;
    peakLevel: number;
    silenceRatio: number;
  };
}

export interface TranscriptionResult {
  /** Transcribed text */
  text: string;
  /** Confidence score (0-1) */
  confidence: number;
  /** Detected language */
  language: string;
  /** Word-level timestamps */
  words?: TranscriptionWord[];
  /** Processing time in milliseconds */
  processingTime: number;
}

export interface TranscriptionWord {
  /** Word text */
  word: string;
  /** Start time in seconds */
  startTime: number;
  /** End time in seconds */
  endTime: number;
  /** Confidence score (0-1) */
  confidence: number;
}

// ============================================================================
// IMAGE CAPTURE TYPES
// ============================================================================

export interface ImageCaptureConfig {
  /** Image quality (0-1) */
  quality: number;
  /** Maximum image width */
  maxWidth: number;
  /** Maximum image height */
  maxHeight: number;
  /** Image format */
  format: 'jpeg' | 'png' | 'webp';
  /** Enable image compression */
  enableCompression: boolean;
  /** Compression quality (0-1) */
  compressionQuality: number;
  /** Enable EXIF data preservation */
  preserveExif: boolean;
}

export interface ImageCaptureResult {
  /** Unique image ID */
  id: string;
  /** Image blob data */
  imageBlob: Blob;
  /** Image metadata */
  metadata: ImageMetadata;
  /** OCR result (if processed) */
  ocrResult?: OCRResult;
  /** Processing status */
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface ImageMetadata {
  /** Image width in pixels */
  width: number;
  /** Image height in pixels */
  height: number;
  /** File size in bytes */
  fileSize: number;
  /** Image format */
  format: string;
  /** Capture timestamp */
  capturedAt: Date;
  /** Device information */
  deviceInfo?: {
    deviceId: string;
    label: string;
    facingMode: 'user' | 'environment';
  };
  /** EXIF data (if available) */
  exifData?: Record<string, any>;
  /** Color analysis */
  colorAnalysis?: {
    dominantColors: string[];
    brightness: number;
    contrast: number;
  };
}

export interface OCRResult {
  /** Extracted text */
  text: string;
  /** Confidence score (0-1) */
  confidence: number;
  /** Detected language */
  language: string;
  /** Text blocks with positioning */
  textBlocks?: OCRTextBlock[];
  /** Processing time in milliseconds */
  processingTime: number;
}

export interface OCRTextBlock {
  /** Block text content */
  text: string;
  /** Bounding box coordinates */
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  /** Confidence score (0-1) */
  confidence: number;
  /** Text orientation angle */
  angle?: number;
}

// ============================================================================
// MEDIA STORAGE TYPES
// ============================================================================

export interface MediaStorageConfig {
  /** Storage provider */
  provider: 'local' | 'cloud' | 'hybrid';
  /** Maximum file size in bytes */
  maxFileSize: number;
  /** Allowed file types */
  allowedTypes: string[];
  /** Automatic cleanup after days */
  autoCleanupDays: number;
  /** Enable encryption */
  enableEncryption: boolean;
  /** Compression settings */
  compression: {
    enabled: boolean;
    quality: number;
    algorithm: 'gzip' | 'brotli' | 'lz4';
  };
}

export interface MediaStorageResult {
  /** Unique storage ID */
  id: string;
  /** File path or URL */
  path: string;
  /** Storage provider used */
  provider: string;
  /** Upload timestamp */
  uploadedAt: Date;
  /** File metadata */
  metadata: {
    originalName: string;
    mimeType: string;
    size: number;
    checksum: string;
  };
}

// ============================================================================
// MEDIA PROCESSING TYPES
// ============================================================================

export interface MediaProcessingJob {
  /** Job ID */
  id: string;
  /** Job type */
  type: 'transcription' | 'ocr' | 'analysis' | 'compression';
  /** Processing status */
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  /** Input media reference */
  inputMedia: {
    id: string;
    type: 'voice' | 'image';
    path: string;
  };
  /** Processing options */
  options: Record<string, any>;
  /** Progress percentage (0-100) */
  progress: number;
  /** Result data */
  result?: any;
  /** Error information */
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  /** Timestamps */
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

export interface MediaProcessingQueue {
  /** Queue name */
  name: string;
  /** Active jobs */
  activeJobs: MediaProcessingJob[];
  /** Pending jobs count */
  pendingCount: number;
  /** Completed jobs count */
  completedCount: number;
  /** Failed jobs count */
  failedCount: number;
  /** Queue status */
  status: 'active' | 'paused' | 'stopped';
}

// ============================================================================
// USER PREFERENCES TYPES
// ============================================================================

export interface MediaPreferences {
  /** Voice recording preferences */
  voice: {
    defaultConfig: Partial<VoiceRecordingConfig>;
    autoTranscribe: boolean;
    transcriptionLanguage: string;
    saveOriginalAudio: boolean;
  };
  /** Image capture preferences */
  image: {
    defaultConfig: Partial<ImageCaptureConfig>;
    autoOCR: boolean;
    ocrLanguage: string;
    saveOriginalImage: boolean;
  };
  /** Storage preferences */
  storage: {
    defaultProvider: 'local' | 'cloud' | 'hybrid';
    autoBackup: boolean;
    compressionEnabled: boolean;
    encryptionEnabled: boolean;
  };
  /** Privacy preferences */
  privacy: {
    allowCloudProcessing: boolean;
    retentionPeriodDays: number;
    shareAnalytics: boolean;
  };
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface MediaApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: Date;
    processingTime: number;
  };
}

export interface MediaUploadResponse extends MediaApiResponse<MediaStorageResult> {
  uploadUrl?: string;
  expiresAt?: Date;
}

export interface MediaProcessingResponse extends MediaApiResponse<MediaProcessingJob> {
  estimatedCompletionTime?: Date;
  queuePosition?: number;
}
