import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  Grid,
  InputAdornment,
  IconButton,
  Divider,
  FormControlLabel,
  Checkbox,
  LinearProgress,
} from '@mui/material';
import {
  EmailRounded,
  LockRounded,
  PersonRounded,
  VisibilityRounded,
  VisibilityOffRounded,
  PersonAddRounded,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

import { useRegisterMutation } from '@/store/api/authApi';
import { useAppDispatch } from '@/store/hooks';
import { setCredentials } from '@/store/slices/authSlice';
import { useNotification } from '@/components/NotificationProvider';
import { ButtonLoading } from '@/components/LoadingScreen';

const validationSchema = Yup.object({
  firstName: Yup.string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters'),
  lastName: Yup.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/\d/, 'Password must contain at least one number')
    .matches(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, 'Password must contain at least one special character')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
  acceptTerms: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions'),
});

const getPasswordStrength = (password: string): number => {
  let strength = 0;
  if (password.length >= 8) strength += 20;
  if (/[a-z]/.test(password)) strength += 20;
  if (/[A-Z]/.test(password)) strength += 20;
  if (/\d/.test(password)) strength += 20;
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) strength += 20;
  return strength;
};

const getPasswordStrengthColor = (strength: number): 'error' | 'warning' | 'success' => {
  if (strength < 40) return 'error';
  if (strength < 80) return 'warning';
  return 'success';
};

const getPasswordStrengthLabel = (strength: number): string => {
  if (strength < 40) return 'Weak';
  if (strength < 80) return 'Medium';
  return 'Strong';
};

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { showSuccess, showError } = useNotification();

  const [register, { isLoading }] = useRegisterMutation();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const { confirmPassword, acceptTerms, ...registerData } = values;
        const result = await register(registerData).unwrap();
        
        dispatch(setCredentials({
          user: result.user,
          tokens: result.tokens,
        }));

        showSuccess(`Welcome to MindSync, ${result.user.firstName || result.user.email}!`);
        navigate('/dashboard', { replace: true });
      } catch (error: any) {
        const errorMessage = error?.data?.error?.message || 'Registration failed. Please try again.';
        showError(errorMessage);
      }
    },
  });

  const passwordStrength = getPasswordStrength(formik.values.password);

  return (
    <Box component="form" onSubmit={formik.handleSubmit} noValidate>
      <Typography
        variant="h5"
        component="h2"
        fontWeight={600}
        textAlign="center"
        mb={3}
      >
        Create Account
      </Typography>

      <Typography
        variant="body2"
        color="text.secondary"
        textAlign="center"
        mb={4}
      >
        Join MindSync and boost your productivity
      </Typography>

      {/* Name Fields */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="firstName"
            name="firstName"
            label="First Name"
            autoComplete="given-name"
            value={formik.values.firstName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.firstName && Boolean(formik.errors.firstName)}
            helperText={formik.touched.firstName && formik.errors.firstName}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonRounded color="action" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="lastName"
            name="lastName"
            label="Last Name"
            autoComplete="family-name"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.lastName && Boolean(formik.errors.lastName)}
            helperText={formik.touched.lastName && formik.errors.lastName}
          />
        </Grid>
      </Grid>

      {/* Email Field */}
      <TextField
        fullWidth
        id="email"
        name="email"
        label="Email Address"
        type="email"
        autoComplete="email"
        value={formik.values.email}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.email && Boolean(formik.errors.email)}
        helperText={formik.touched.email && formik.errors.email}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <EmailRounded color="action" />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 2 }}
      />

      {/* Password Field */}
      <TextField
        fullWidth
        id="password"
        name="password"
        label="Password"
        type={showPassword ? 'text' : 'password'}
        autoComplete="new-password"
        value={formik.values.password}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.password && Boolean(formik.errors.password)}
        helperText={formik.touched.password && formik.errors.password}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <LockRounded color="action" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={() => setShowPassword(!showPassword)}
                edge="end"
              >
                {showPassword ? <VisibilityOffRounded /> : <VisibilityRounded />}
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{ mb: 1 }}
      />

      {/* Password Strength Indicator */}
      {formik.values.password && (
        <Box sx={{ mb: 2 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
            <Typography variant="caption" color="text.secondary">
              Password Strength
            </Typography>
            <Typography
              variant="caption"
              color={`${getPasswordStrengthColor(passwordStrength)}.main`}
              fontWeight={600}
            >
              {getPasswordStrengthLabel(passwordStrength)}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={passwordStrength}
            color={getPasswordStrengthColor(passwordStrength)}
            sx={{ height: 4, borderRadius: 2 }}
          />
        </Box>
      )}

      {/* Confirm Password Field */}
      <TextField
        fullWidth
        id="confirmPassword"
        name="confirmPassword"
        label="Confirm Password"
        type={showConfirmPassword ? 'text' : 'password'}
        autoComplete="new-password"
        value={formik.values.confirmPassword}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
        helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <LockRounded color="action" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle confirm password visibility"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                edge="end"
              >
                {showConfirmPassword ? <VisibilityOffRounded /> : <VisibilityRounded />}
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{ mb: 2 }}
      />

      {/* Terms and Conditions */}
      <FormControlLabel
        control={
          <Checkbox
            id="acceptTerms"
            name="acceptTerms"
            checked={formik.values.acceptTerms}
            onChange={formik.handleChange}
            color="primary"
          />
        }
        label={
          <Typography variant="body2" color="text.secondary">
            I agree to the{' '}
            <Link href="/terms" target="_blank" color="primary" underline="hover">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" target="_blank" color="primary" underline="hover">
              Privacy Policy
            </Link>
          </Typography>
        }
        sx={{ mb: 3 }}
      />
      {formik.touched.acceptTerms && formik.errors.acceptTerms && (
        <Typography variant="caption" color="error" display="block" sx={{ mt: -2, mb: 2 }}>
          {formik.errors.acceptTerms}
        </Typography>
      )}

      {/* Register Button */}
      <Button
        type="submit"
        fullWidth
        variant="contained"
        size="large"
        disabled={isLoading}
        startIcon={<PersonAddRounded />}
        sx={{ mb: 2, py: 1.5 }}
      >
        <ButtonLoading loading={isLoading}>
          Create Account
        </ButtonLoading>
      </Button>

      <Divider sx={{ mb: 3 }}>
        <Typography variant="body2" color="text.secondary">
          or
        </Typography>
      </Divider>

      {/* Sign In Link */}
      <Box textAlign="center">
        <Typography variant="body2" color="text.secondary">
          Already have an account?{' '}
          <Link
            component={RouterLink}
            to="/login"
            color="primary"
            underline="hover"
            fontWeight={600}
          >
            Sign in
          </Link>
        </Typography>
      </Box>
    </Box>
  );
};
