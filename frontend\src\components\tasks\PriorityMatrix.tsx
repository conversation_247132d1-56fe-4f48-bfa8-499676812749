import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  PriorityHighRounded,
  ScheduleRounded,
  PersonRounded,
  DeleteRounded,
  SettingsRounded,
  RefreshRounded,
  InfoRounded,
  TrendingUpRounded,
  TrendingDownRounded,
} from '@mui/icons-material';

import type { Task } from '@/store/api/tasksApi';
import type { PriorityMatrix, TaskPriorityScore } from '@/types/advanced-tasks';
import { useNotification } from '@/components/NotificationProvider';

/**
 * Priority Matrix Component (Eisenhower Matrix)
 * TODO: Implement actual API integration and drag-and-drop functionality
 */

interface PriorityMatrixProps {
  tasks: Task[];
  onTaskUpdate?: (taskId: string, updates: Partial<Task>) => void;
  onRefresh?: () => void;
  className?: string;
}

interface MatrixQuadrant {
  key: keyof PriorityMatrix['quadrants'];
  title: string;
  subtitle: string;
  color: 'error' | 'warning' | 'info' | 'success';
  icon: React.ReactNode;
  actionLabel: string;
}

const QUADRANTS: MatrixQuadrant[] = [
  {
    key: 'urgent_important',
    title: 'Do First',
    subtitle: 'Urgent & Important',
    color: 'error',
    icon: <PriorityHighRounded />,
    actionLabel: 'Handle immediately',
  },
  {
    key: 'not_urgent_important',
    title: 'Schedule',
    subtitle: 'Important, Not Urgent',
    color: 'warning',
    icon: <ScheduleRounded />,
    actionLabel: 'Plan and schedule',
  },
  {
    key: 'urgent_not_important',
    title: 'Delegate',
    subtitle: 'Urgent, Not Important',
    color: 'info',
    icon: <PersonRounded />,
    actionLabel: 'Delegate if possible',
  },
  {
    key: 'not_urgent_not_important',
    title: 'Eliminate',
    subtitle: 'Neither Urgent nor Important',
    color: 'success',
    icon: <DeleteRounded />,
    actionLabel: 'Consider eliminating',
  },
];

export const PriorityMatrix: React.FC<PriorityMatrixProps> = ({
  tasks,
  onTaskUpdate,
  onRefresh,
  className,
}) => {
  const { showSuccess, showError } = useNotification();
  
  // State
  const [matrix, setMatrix] = useState<PriorityMatrix | null>(null);
  const [loading, setLoading] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [scoringPreferences, setScoringPreferences] = useState({
    dueDateWeight: 30,
    priorityWeight: 25,
    goalAlignmentWeight: 25,
    complexityWeight: 20,
  });

  // Generate matrix on tasks change
  useEffect(() => {
    if (tasks.length > 0) {
      generateMatrix();
    }
  }, [tasks, scoringPreferences]);

  // Generate priority matrix
  const generateMatrix = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      const mockMatrix = await generateMockMatrix(tasks, scoringPreferences);
      setMatrix(mockMatrix);
      
    } catch (error) {
      console.error('Error generating priority matrix:', error);
      showError('Failed to generate priority matrix');
    } finally {
      setLoading(false);
    }
  };

  // Handle task priority update
  const handleTaskPriorityUpdate = async (taskId: string, newQuadrant: string) => {
    try {
      // TODO: Implement actual priority update logic
      showSuccess('Task priority updated');
      onTaskUpdate?.(taskId, { metadata: { priorityQuadrant: newQuadrant } });
    } catch (error) {
      showError('Failed to update task priority');
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    generateMatrix();
    onRefresh?.();
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  // Render task item
  const renderTaskItem = (task: Task, quadrantKey: string) => (
    <ListItem
      key={task.id}
      sx={{
        mb: 1,
        borderRadius: 1,
        bgcolor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        '&:hover': {
          bgcolor: 'action.hover',
        },
      }}
    >
      <ListItemIcon sx={{ minWidth: 36 }}>
        <Chip
          label={task.priority}
          size="small"
          color={getPriorityColor(task.priority)}
          variant="outlined"
        />
      </ListItemIcon>
      
      <ListItemText
        primary={
          <Typography variant="body2" fontWeight={500}>
            {task.title}
          </Typography>
        }
        secondary={
          <Box display="flex" alignItems="center" gap={1} mt={0.5}>
            {task.dueDate && (
              <Chip
                label={new Date(task.dueDate).toLocaleDateString()}
                size="small"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.625rem' }}
              />
            )}
            <Chip
              label={`${task.complexityScore}/10`}
              size="small"
              variant="outlined"
              sx={{ height: 20, fontSize: '0.625rem' }}
            />
          </Box>
        }
      />
      
      <Tooltip title="Task details">
        <IconButton size="small">
          <InfoRounded fontSize="small" />
        </IconButton>
      </Tooltip>
    </ListItem>
  );

  // Render quadrant
  const renderQuadrant = (quadrant: MatrixQuadrant) => {
    const quadrantTasks = matrix?.quadrants[quadrant.key] || [];
    
    return (
      <Card
        key={quadrant.key}
        sx={{
          height: '100%',
          border: 2,
          borderColor: `${quadrant.color}.main`,
          borderStyle: 'solid',
        }}
      >
        <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Quadrant Header */}
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <Box
              sx={{
                p: 1,
                borderRadius: 1,
                bgcolor: `${quadrant.color}.main`,
                color: `${quadrant.color}.contrastText`,
              }}
            >
              {quadrant.icon}
            </Box>
            <Box flex={1}>
              <Typography variant="h6" fontWeight={600}>
                {quadrant.title}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {quadrant.subtitle}
              </Typography>
            </Box>
            <Chip
              label={quadrantTasks.length}
              size="small"
              color={quadrant.color}
              variant="outlined"
            />
          </Box>

          {/* Action Label */}
          <Box mb={2}>
            <Typography variant="body2" color="text.secondary" fontStyle="italic">
              {quadrant.actionLabel}
            </Typography>
          </Box>

          {/* Task List */}
          <Box flex={1} sx={{ overflowY: 'auto' }}>
            {quadrantTasks.length > 0 ? (
              <List disablePadding>
                {quadrantTasks.map((task: Task) => renderTaskItem(task, quadrant.key))}
              </List>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                height="100%"
                color="text.secondary"
              >
                <Typography variant="body2">
                  No tasks in this quadrant
                </Typography>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    );
  };

  if (!matrix && !loading) {
    return (
      <Card className={className}>
        <CardContent>
          <Box textAlign="center" py={4}>
            <Typography variant="h6" gutterBottom>
              Priority Matrix
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={3}>
              Organize your tasks using the Eisenhower Matrix
            </Typography>
            <Button
              variant="contained"
              onClick={generateMatrix}
              startIcon={<TrendingUpRounded />}
            >
              Generate Matrix
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box className={className}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Priority Matrix
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Eisenhower Matrix for task prioritization
          </Typography>
        </Box>
        
        <Box display="flex" gap={1}>
          <Tooltip title="Refresh matrix">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshRounded />
            </IconButton>
          </Tooltip>
          <Tooltip title="Matrix settings">
            <IconButton onClick={() => setSettingsOpen(true)}>
              <SettingsRounded />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Matrix Grid */}
      {loading ? (
        <Box display="flex" justifyContent="center" py={8}>
          <Typography>Generating priority matrix...</Typography>
        </Box>
      ) : (
        <Grid container spacing={2} sx={{ height: 600 }}>
          {QUADRANTS.map((quadrant) => (
            <Grid item xs={12} md={6} key={quadrant.key} sx={{ height: '50%' }}>
              {renderQuadrant(quadrant)}
            </Grid>
          ))}
        </Grid>
      )}

      {/* Matrix Statistics */}
      {matrix && (
        <Box mt={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Matrix Statistics
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="error.main">
                      {matrix.quadrants.urgent_important.length}
                    </Typography>
                    <Typography variant="caption">Do First</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="warning.main">
                      {matrix.quadrants.not_urgent_important.length}
                    </Typography>
                    <Typography variant="caption">Schedule</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="info.main">
                      {matrix.quadrants.urgent_not_important.length}
                    </Typography>
                    <Typography variant="caption">Delegate</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="success.main">
                      {matrix.quadrants.not_urgent_not_important.length}
                    </Typography>
                    <Typography variant="caption">Eliminate</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Settings Dialog */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Priority Matrix Settings</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={3} pt={1}>
            <Typography variant="subtitle2">
              Scoring Weights (Total: {Object.values(scoringPreferences).reduce((a, b) => a + b, 0)}%)
            </Typography>
            
            <Box>
              <Typography variant="body2" gutterBottom>
                Due Date Weight: {scoringPreferences.dueDateWeight}%
              </Typography>
              <Slider
                value={scoringPreferences.dueDateWeight}
                onChange={(_, value) => setScoringPreferences(prev => ({ ...prev, dueDateWeight: value as number }))}
                min={0}
                max={50}
                step={5}
                marks
                valueLabelDisplay="auto"
              />
            </Box>

            <Box>
              <Typography variant="body2" gutterBottom>
                Priority Weight: {scoringPreferences.priorityWeight}%
              </Typography>
              <Slider
                value={scoringPreferences.priorityWeight}
                onChange={(_, value) => setScoringPreferences(prev => ({ ...prev, priorityWeight: value as number }))}
                min={0}
                max={50}
                step={5}
                marks
                valueLabelDisplay="auto"
              />
            </Box>

            <Box>
              <Typography variant="body2" gutterBottom>
                Goal Alignment Weight: {scoringPreferences.goalAlignmentWeight}%
              </Typography>
              <Slider
                value={scoringPreferences.goalAlignmentWeight}
                onChange={(_, value) => setScoringPreferences(prev => ({ ...prev, goalAlignmentWeight: value as number }))}
                min={0}
                max={50}
                step={5}
                marks
                valueLabelDisplay="auto"
              />
            </Box>

            <Box>
              <Typography variant="body2" gutterBottom>
                Complexity Weight: {scoringPreferences.complexityWeight}%
              </Typography>
              <Slider
                value={scoringPreferences.complexityWeight}
                onChange={(_, value) => setScoringPreferences(prev => ({ ...prev, complexityWeight: value as number }))}
                min={0}
                max={50}
                step={5}
                marks
                valueLabelDisplay="auto"
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={() => {
            setSettingsOpen(false);
            generateMatrix();
          }}>
            Apply Settings
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Mock matrix generation function
async function generateMockMatrix(tasks: Task[], preferences: any): Promise<PriorityMatrix> {
  // TODO: Replace with actual API call
  return new Promise((resolve) => {
    setTimeout(() => {
      const shuffledTasks = [...tasks].sort(() => Math.random() - 0.5);
      const quadrantSize = Math.ceil(tasks.length / 4);
      
      resolve({
        quadrants: {
          urgent_important: shuffledTasks.slice(0, quadrantSize),
          not_urgent_important: shuffledTasks.slice(quadrantSize, quadrantSize * 2),
          urgent_not_important: shuffledTasks.slice(quadrantSize * 2, quadrantSize * 3),
          not_urgent_not_important: shuffledTasks.slice(quadrantSize * 3),
        },
        metadata: {
          totalTasks: tasks.length,
          lastUpdated: new Date(),
          scoringPreferences: {
            dueDateWeight: preferences.dueDateWeight / 100,
            priorityWeight: preferences.priorityWeight / 100,
            goalAlignmentWeight: preferences.goalAlignmentWeight / 100,
            complexityWeight: preferences.complexityWeight / 100,
          },
        },
      });
    }, 1000);
  });
}
