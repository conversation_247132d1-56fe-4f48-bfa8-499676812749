import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';

export const GoalsPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Goals' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Goals Management
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Create, track, and manage your goals with hierarchical organization.
      </Typography>

      <Alert severity="info">
        <Typography variant="body2">
          Goals management interface is coming soon! This will include goal creation, progress tracking, and hierarchical goal organization.
        </Typography>
      </Alert>
    </Box>
  );
};
