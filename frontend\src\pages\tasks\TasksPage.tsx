import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';

export const TasksPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Tasks' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Task Management
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your tasks with smart prioritization and complexity analysis.
      </Typography>

      <Alert severity="info">
        <Typography variant="body2">
          Task management interface is coming soon! This will include task creation, kanban boards, and AI-powered task recommendations.
        </Typography>
      </Alert>
    </Box>
  );
};
