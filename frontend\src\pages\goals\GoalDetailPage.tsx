import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';

export const GoalDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Goals', path: '/goals' },
      { label: 'Goal Details' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Goal Details
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Detailed view and management for goal: {id}
      </Typography>

      <Alert severity="info">
        <Typography variant="body2">
          Goal detail interface is coming soon! This will include detailed goal information, progress tracking, and task management.
        </Typography>
      </Alert>
    </Box>
  );
};
