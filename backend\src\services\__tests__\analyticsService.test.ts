import { analyticsService } from '../analyticsService';
import { logger } from '@/utils/logger';

/**
 * Unit tests for Analytics Service
 * TODO: Implement comprehensive tests for all analytics features
 */

// Mock dependencies
jest.mock('@/utils/logger');
jest.mock('@/config/database');

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  writable: true,
  value: {
    randomUUID: jest.fn().mockReturnValue('test-uuid-123'),
  },
});

describe('AnalyticsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Event Tracking', () => {
    describe('trackEvent', () => {
      it('should track a single event successfully', async () => {
        const eventData = {
          userId: 'user-123',
          eventType: 'task_completed',
          data: { taskId: 'task-456', duration: 30 },
          source: 'user_action' as const,
        };

        await analyticsService.trackEvent(eventData);

        expect(logger.info).toHaveBeenCalledWith(
          'Tracking event: task_completed for user: user-123'
        );
        expect(logger.debug).toHaveBeenCalledWith(
          'Event tracked successfully: test-uuid-123'
        );
      });

      it('should handle event with session ID', async () => {
        const eventData = {
          userId: 'user-123',
          eventType: 'page_view',
          data: { page: '/dashboard' },
          sessionId: 'session-789',
          source: 'user_action' as const,
        };

        await analyticsService.trackEvent(eventData);

        expect(logger.info).toHaveBeenCalledWith(
          'Tracking event: page_view for user: user-123'
        );
      });
    });

    describe('trackEventsBatch', () => {
      it('should track multiple events in batch', async () => {
        const events = [
          {
            userId: 'user-123',
            eventType: 'task_created',
            data: { taskId: 'task-1' },
            source: 'user_action' as const,
          },
          {
            userId: 'user-123',
            eventType: 'task_updated',
            data: { taskId: 'task-1', field: 'title' },
            source: 'user_action' as const,
          },
        ];

        await analyticsService.trackEventsBatch(events);

        expect(logger.info).toHaveBeenCalledWith('Batch tracking 2 events');
        expect(logger.info).toHaveBeenCalledWith('Batch tracking completed: 2 events');
      });

      it('should handle empty batch', async () => {
        const events: any[] = [];

        await analyticsService.trackEventsBatch(events);

        expect(logger.info).toHaveBeenCalledWith('Batch tracking 0 events');
        expect(logger.info).toHaveBeenCalledWith('Batch tracking completed: 0 events');
      });
    });
  });

  describe('Metrics', () => {
    describe('getMetrics', () => {
      it('should return all metrics when no category specified', async () => {
        const metrics = await analyticsService.getMetrics();

        expect(metrics).toHaveLength(3);
        expect(metrics[0]).toMatchObject({
          id: 'tasks_completed',
          name: 'Tasks Completed',
          category: 'productivity',
          dataType: 'count',
          unit: 'tasks',
        });
      });

      it('should filter metrics by category', async () => {
        const metrics = await analyticsService.getMetrics('productivity');

        expect(metrics).toHaveLength(2);
        expect(metrics.every(m => m.category === 'productivity')).toBe(true);
      });

      it('should return empty array for non-existent category', async () => {
        const metrics = await analyticsService.getMetrics('non_existent');

        expect(metrics).toHaveLength(0);
      });
    });
  });

  describe('Query Execution', () => {
    describe('executeQuery', () => {
      it('should execute analytics query successfully', async () => {
        const query = {
          id: 'query-123',
          metrics: ['tasks_completed'],
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31'),
          },
          filters: [],
          groupBy: [],
          aggregations: [],
          orderBy: [],
        };

        const result = await analyticsService.executeQuery(query);

        expect(result).toMatchObject({
          queryId: 'query-123',
          data: [],
          metadata: {
            totalRows: 0,
            executionTime: expect.any(Number),
            cacheHit: false,
            dataFreshness: expect.any(Date),
          },
          columns: [],
        });
        expect(logger.info).toHaveBeenCalledWith('Executing analytics query: query-123');
      });

      it('should validate query parameters', async () => {
        const invalidQuery = {
          id: 'query-123',
          metrics: [], // Empty metrics array
          timeRange: {
            start: new Date('2024-01-31'),
            end: new Date('2024-01-01'), // End before start
          },
          filters: [],
          groupBy: [],
          aggregations: [],
          orderBy: [],
        };

        await expect(analyticsService.executeQuery(invalidQuery))
          .rejects.toThrow('Failed to execute analytics query');
      });

      it('should handle query with filters and grouping', async () => {
        const query = {
          id: 'query-456',
          metrics: ['tasks_completed', 'completion_rate'],
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31'),
          },
          filters: [
            { field: 'priority', operator: 'eq' as const, value: 'high' },
          ],
          groupBy: ['date'],
          aggregations: [
            { function: 'sum' as const, field: 'tasks_completed' },
          ],
          orderBy: [
            { field: 'date', direction: 'desc' as const },
          ],
        };

        const result = await analyticsService.executeQuery(query);

        expect(result.queryId).toBe('query-456');
        expect(logger.info).toHaveBeenCalledWith('Executing analytics query: query-456');
      });
    });
  });

  describe('Dashboard Management', () => {
    describe('createDashboard', () => {
      it('should create dashboard successfully', async () => {
        const dashboardData = {
          name: 'Test Dashboard',
          description: 'Test dashboard description',
          userId: 'user-123',
          widgets: [],
          layout: { type: 'grid' as const },
          settings: {},
          isPublic: false,
          tags: ['test'],
        };

        const dashboard = await analyticsService.createDashboard(dashboardData);

        expect(dashboard).toMatchObject({
          id: 'test-uuid-123',
          name: 'Test Dashboard',
          description: 'Test dashboard description',
          userId: 'user-123',
          isPublic: false,
          tags: ['test'],
        });
        expect(dashboard.createdAt).toBeInstanceOf(Date);
        expect(dashboard.updatedAt).toBeInstanceOf(Date);
        expect(logger.info).toHaveBeenCalledWith(
          'Creating dashboard: Test Dashboard for user: user-123'
        );
      });

      it('should handle dashboard without description', async () => {
        const dashboardData = {
          name: 'Simple Dashboard',
          userId: 'user-123',
          widgets: [],
          layout: { type: 'grid' as const },
          settings: {},
          isPublic: true,
          tags: [],
        };

        const dashboard = await analyticsService.createDashboard(dashboardData);

        expect(dashboard.description).toBeUndefined();
        expect(dashboard.isPublic).toBe(true);
        expect(dashboard.tags).toEqual([]);
      });
    });

    describe('getUserDashboards', () => {
      it('should return user dashboards', async () => {
        const userId = 'user-123';

        const dashboards = await analyticsService.getUserDashboards(userId);

        expect(Array.isArray(dashboards)).toBe(true);
        expect(logger.info).toHaveBeenCalledWith(
          'Getting dashboards for user: user-123'
        );
      });
    });

    describe('updateDashboard', () => {
      it('should update dashboard successfully', async () => {
        const dashboardId = 'dashboard-123';
        const updates = {
          name: 'Updated Dashboard',
          description: 'Updated description',
        };

        // This will fail in current implementation since getDashboard returns null
        await expect(analyticsService.updateDashboard(dashboardId, updates))
          .rejects.toThrow('Failed to update dashboard');

        expect(logger.info).toHaveBeenCalledWith(
          'Updating dashboard: dashboard-123'
        );
      });
    });
  });

  describe('Report Management', () => {
    describe('createReport', () => {
      it('should create report successfully', async () => {
        const reportData = {
          name: 'Test Report',
          description: 'Test report description',
          type: 'productivity' as const,
          userId: 'user-123',
          configuration: {},
          recipients: [],
          status: 'draft' as const,
        };

        const report = await analyticsService.createReport(reportData);

        expect(report).toMatchObject({
          id: 'test-uuid-123',
          name: 'Test Report',
          type: 'productivity',
          userId: 'user-123',
          status: 'draft',
        });
        expect(report.createdAt).toBeInstanceOf(Date);
        expect(report.updatedAt).toBeInstanceOf(Date);
        expect(logger.info).toHaveBeenCalledWith(
          'Creating report: Test Report for user: user-123'
        );
      });

      it('should handle report with schedule', async () => {
        const reportData = {
          name: 'Scheduled Report',
          type: 'tasks' as const,
          userId: 'user-123',
          configuration: {},
          schedule: {
            type: 'recurring' as const,
            recurrence: {
              frequency: 'weekly' as const,
              interval: 1,
              time: '09:00',
            },
            timezone: 'UTC',
          },
          recipients: [],
          status: 'active' as const,
        };

        const report = await analyticsService.createReport(reportData);

        expect(report.schedule).toBeDefined();
        expect(logger.debug).toHaveBeenCalledWith(
          'Scheduling report: test-uuid-123'
        );
      });
    });

    describe('generateReport', () => {
      it('should generate report successfully', async () => {
        const reportId = 'report-123';

        // This will fail in current implementation since getReport returns null
        await expect(analyticsService.generateReport(reportId))
          .rejects.toThrow('Failed to generate report');

        expect(logger.info).toHaveBeenCalledWith(
          'Generating report: report-123'
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Mock logger.error to throw an error
      const originalError = logger.error;
      (logger.error as jest.Mock).mockImplementation(() => {
        throw new Error('Logger error');
      });

      const eventData = {
        userId: 'user-123',
        eventType: 'test_event',
        data: {},
        source: 'user_action' as const,
      };

      await expect(analyticsService.trackEvent(eventData))
        .rejects.toThrow('Failed to track event');

      // Restore original logger
      logger.error = originalError;
    });

    it('should handle invalid query gracefully', async () => {
      const invalidQuery = {
        id: 'invalid-query',
        metrics: [], // Invalid: empty metrics
        timeRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
        filters: [],
        groupBy: [],
        aggregations: [],
        orderBy: [],
      };

      await expect(analyticsService.executeQuery(invalidQuery))
        .rejects.toThrow('Failed to execute analytics query');
    });
  });
});
