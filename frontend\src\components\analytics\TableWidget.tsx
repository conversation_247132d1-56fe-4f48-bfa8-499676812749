import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  MoreVertRounded,
  EditRounded,
  DeleteRounded,
  RefreshRounded,
  TableViewRounded,
} from '@mui/icons-material';

import type { DashboardWidget } from '@/types/analytics';

/**
 * Table Widget Component - Displays data in table format
 * TODO: Implement actual data table with sorting, filtering, and pagination
 */

interface TableWidgetProps {
  widget: DashboardWidget;
  editMode: boolean;
  onUpdate: (updates: Partial<DashboardWidget>) => void;
  onDelete: () => void;
}

export const TableWidget: React.FC<TableWidgetProps> = ({
  widget,
  editMode,
  onUpdate,
  onDelete,
}) => {
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            {widget.title}
          </Typography>
          
          {editMode && (
            <IconButton
              size="small"
              onClick={(e) => setMenuAnchor(e.currentTarget)}
            >
              <MoreVertRounded />
            </IconButton>
          )}
        </Box>

        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" height={200}>
          <TableViewRounded sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="body2" color="text.secondary">
            Data table will be implemented here
          </Typography>
          <Typography variant="caption" color="text.secondary" mt={1}>
            TODO: Implement data table component
          </Typography>
        </Box>
      </CardContent>

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <EditRounded sx={{ mr: 1 }} />
          Configure
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <RefreshRounded sx={{ mr: 1 }} />
          Refresh
        </MenuItem>
        <MenuItem onClick={() => {
          onDelete();
          setMenuAnchor(null);
        }}>
          <DeleteRounded sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Card>
  );
};
