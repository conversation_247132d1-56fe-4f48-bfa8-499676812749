import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { authMiddleware, AuthenticatedRequest, authService, validatePassword } from '@/middleware/auth';
import { asyncHandler, ValidationError, NotFoundError } from '@/middleware/errorHandler';
import { executeQuery } from '@/config/database';
import { logger, logBusinessEvent } from '@/utils/logger';

const router = Router();

/**
 * @swagger
 * /api/users/profile:
 *   get:
 *     summary: Get user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 */
router.get('/profile',
  authMiddleware,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = await authService.getUserById(req.user!.id);
    
    if (!user) {
      throw new NotFoundError('User not found');
    }

    res.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        isVerified: user.is_verified,
        avatarUrl: user.avatar_url,
        timezone: user.timezone,
        preferences: user.preferences,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      }
    });
  })
);

/**
 * @swagger
 * /api/users/profile:
 *   put:
 *     summary: Update user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 maxLength: 100
 *               lastName:
 *                 type: string
 *                 maxLength: 100
 *               timezone:
 *                 type: string
 *                 maxLength: 50
 *               preferences:
 *                 type: object
 *     responses:
 *       200:
 *         description: Profile updated successfully
 */
router.put('/profile',
  authMiddleware,
  [
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('First name must be between 1 and 100 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Last name must be between 1 and 100 characters'),
    body('timezone')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Timezone must be between 1 and 50 characters'),
    body('preferences')
      .optional()
      .isObject()
      .withMessage('Preferences must be an object')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { firstName, lastName, timezone, preferences } = req.body;

    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (firstName !== undefined) {
      updateFields.push(`first_name = $${paramIndex++}`);
      updateValues.push(firstName);
    }

    if (lastName !== undefined) {
      updateFields.push(`last_name = $${paramIndex++}`);
      updateValues.push(lastName);
    }

    if (timezone !== undefined) {
      updateFields.push(`timezone = $${paramIndex++}`);
      updateValues.push(timezone);
    }

    if (preferences !== undefined) {
      updateFields.push(`preferences = $${paramIndex++}`);
      updateValues.push(JSON.stringify(preferences));
    }

    if (updateFields.length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    updateFields.push(`updated_at = NOW()`);
    updateValues.push(userId);

    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, email, first_name, last_name, role, is_verified, 
                avatar_url, timezone, preferences, updated_at
    `;

    const result = await executeQuery(query, updateValues);
    const updatedUser = result.rows[0];

    logBusinessEvent('Profile updated', userId, {
      updatedFields: Object.keys(req.body)
    });

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        role: updatedUser.role,
        isVerified: updatedUser.is_verified,
        avatarUrl: updatedUser.avatar_url,
        timezone: updatedUser.timezone,
        preferences: updatedUser.preferences,
        updatedAt: updatedUser.updated_at
      }
    });
  })
);

/**
 * @swagger
 * /api/users/change-password:
 *   put:
 *     summary: Change user password
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Password changed successfully
 */
router.put('/change-password',
  authMiddleware,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { currentPassword, newPassword } = req.body;

    // Validate new password strength
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      throw new ValidationError(passwordValidation.errors.join(', '));
    }

    // Get current user with password hash
    const user = await authService.getUserByEmail(req.user!.email);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await authService.verifyPassword(currentPassword, user.password_hash);
    if (!isCurrentPasswordValid) {
      throw new ValidationError('Current password is incorrect');
    }

    // Hash new password
    const newPasswordHash = await authService.hashPassword(newPassword);

    // Update password
    const query = `
      UPDATE users 
      SET password_hash = $1, updated_at = NOW()
      WHERE id = $2
    `;

    await executeQuery(query, [newPasswordHash, userId]);

    logBusinessEvent('Password changed', userId);

    res.json({
      message: 'Password changed successfully'
    });
  })
);

/**
 * @swagger
 * /api/users/stats:
 *   get:
 *     summary: Get user statistics
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User statistics retrieved successfully
 */
router.get('/stats',
  authMiddleware,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;

    // Get capture statistics
    const captureStatsQuery = `
      SELECT 
        type,
        COUNT(*) as count,
        COUNT(CASE WHEN is_processed = true THEN 1 END) as processed_count
      FROM captures 
      WHERE user_id = $1 
      GROUP BY type
    `;

    const captureStats = await executeQuery(captureStatsQuery, [userId]);

    // Get goal statistics
    const goalStatsQuery = `
      SELECT 
        status,
        COUNT(*) as count
      FROM goals 
      WHERE user_id = $1 
      GROUP BY status
    `;

    const goalStats = await executeQuery(goalStatsQuery, [userId]);

    // Get task statistics
    const taskStatsQuery = `
      SELECT 
        status,
        priority,
        COUNT(*) as count,
        AVG(complexity_score) as avg_complexity
      FROM tasks 
      WHERE user_id = $1 
      GROUP BY status, priority
    `;

    const taskStats = await executeQuery(taskStatsQuery, [userId]);

    // Get recent activity
    const recentActivityQuery = `
      SELECT 
        'capture' as activity_type,
        type as activity_subtype,
        created_at
      FROM captures 
      WHERE user_id = $1
      UNION ALL
      SELECT 
        'goal' as activity_type,
        status as activity_subtype,
        created_at
      FROM goals 
      WHERE user_id = $1
      UNION ALL
      SELECT 
        'task' as activity_type,
        status as activity_subtype,
        created_at
      FROM tasks 
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 10
    `;

    const recentActivity = await executeQuery(recentActivityQuery, [userId]);

    res.json({
      stats: {
        captures: captureStats.rows,
        goals: goalStats.rows,
        tasks: taskStats.rows,
        recentActivity: recentActivity.rows
      }
    });
  })
);

/**
 * @swagger
 * /api/users/preferences:
 *   get:
 *     summary: Get user preferences
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User preferences retrieved successfully
 */
router.get('/preferences',
  authMiddleware,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = await authService.getUserById(req.user!.id);
    
    if (!user) {
      throw new NotFoundError('User not found');
    }

    res.json({
      preferences: user.preferences || {}
    });
  })
);

/**
 * @swagger
 * /api/users/preferences:
 *   put:
 *     summary: Update user preferences
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               theme:
 *                 type: string
 *                 enum: [light, dark, auto]
 *               language:
 *                 type: string
 *               notifications:
 *                 type: object
 *               dashboard:
 *                 type: object
 *               capture:
 *                 type: object
 *     responses:
 *       200:
 *         description: Preferences updated successfully
 */
router.put('/preferences',
  authMiddleware,
  [
    body('theme')
      .optional()
      .isIn(['light', 'dark', 'auto'])
      .withMessage('Theme must be light, dark, or auto'),
    body('language')
      .optional()
      .isLength({ min: 2, max: 5 })
      .withMessage('Language must be a valid language code'),
    body('notifications')
      .optional()
      .isObject()
      .withMessage('Notifications must be an object'),
    body('dashboard')
      .optional()
      .isObject()
      .withMessage('Dashboard preferences must be an object'),
    body('capture')
      .optional()
      .isObject()
      .withMessage('Capture preferences must be an object')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const newPreferences = req.body;

    // Get current preferences
    const user = await authService.getUserById(userId);
    const currentPreferences = user?.preferences || {};

    // Merge preferences
    const updatedPreferences = {
      ...currentPreferences,
      ...newPreferences
    };

    // Update in database
    const query = `
      UPDATE users 
      SET preferences = $1, updated_at = NOW()
      WHERE id = $2
      RETURNING preferences
    `;

    const result = await executeQuery(query, [JSON.stringify(updatedPreferences), userId]);

    logBusinessEvent('Preferences updated', userId, {
      updatedKeys: Object.keys(newPreferences)
    });

    res.json({
      message: 'Preferences updated successfully',
      preferences: result.rows[0].preferences
    });
  })
);

/**
 * @swagger
 * /api/users/delete-account:
 *   delete:
 *     summary: Delete user account
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *               - confirmation
 *             properties:
 *               password:
 *                 type: string
 *               confirmation:
 *                 type: string
 *                 enum: [DELETE_MY_ACCOUNT]
 *     responses:
 *       200:
 *         description: Account deleted successfully
 */
router.delete('/delete-account',
  authMiddleware,
  [
    body('password')
      .notEmpty()
      .withMessage('Password is required'),
    body('confirmation')
      .equals('DELETE_MY_ACCOUNT')
      .withMessage('Confirmation must be "DELETE_MY_ACCOUNT"')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { password } = req.body;

    // Get user with password hash
    const user = await authService.getUserByEmail(req.user!.email);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify password
    const isPasswordValid = await authService.verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw new ValidationError('Password is incorrect');
    }

    // Delete user account (CASCADE will handle related data)
    const query = `DELETE FROM users WHERE id = $1`;
    await executeQuery(query, [userId]);

    logBusinessEvent('Account deleted', userId);

    res.json({
      message: 'Account deleted successfully'
    });
  })
);

export default router;
