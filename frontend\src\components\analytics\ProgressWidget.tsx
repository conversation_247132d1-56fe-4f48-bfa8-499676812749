import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
  CircularProgress,
} from '@mui/material';
import {
  MoreVertRounded,
  EditRounded,
  DeleteRounded,
  RefreshRounded,
} from '@mui/icons-material';

import type { DashboardWidget } from '@/types/analytics';

/**
 * Progress Widget Component - Displays progress indicators
 * TODO: Implement advanced progress visualizations and real-time updates
 */

interface ProgressWidgetProps {
  widget: DashboardWidget;
  editMode: boolean;
  onUpdate: (updates: Partial<DashboardWidget>) => void;
  onDelete: () => void;
}

export const ProgressWidget: React.FC<ProgressWidgetProps> = ({
  widget,
  editMode,
  onUpdate,
  onDelete,
}) => {
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  
  // Mock progress data
  const progressValue = 65;
  const maxValue = 100;
  const progressConfig = widget.configuration.progress || {
    type: 'linear',
    showPercentage: true,
    showValue: true,
    colorScheme: 'default',
  };

  const renderProgressIndicator = () => {
    const percentage = (progressValue / maxValue) * 100;
    
    if (progressConfig.type === 'circular') {
      return (
        <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
          <Box position="relative" display="inline-flex">
            <CircularProgress
              variant="determinate"
              value={percentage}
              size={120}
              thickness={6}
              color="primary"
            />
            <Box
              position="absolute"
              top={0}
              left={0}
              bottom={0}
              right={0}
              display="flex"
              alignItems="center"
              justifyContent="center"
              flexDirection="column"
            >
              {progressConfig.showValue && (
                <Typography variant="h6" fontWeight={600}>
                  {progressValue}
                </Typography>
              )}
              {progressConfig.showPercentage && (
                <Typography variant="caption" color="text.secondary">
                  {percentage.toFixed(0)}%
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
      );
    }
    
    return (
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          {progressConfig.showValue && (
            <Typography variant="body2">
              {progressValue} / {maxValue}
            </Typography>
          )}
          {progressConfig.showPercentage && (
            <Typography variant="body2" color="text.secondary">
              {percentage.toFixed(0)}%
            </Typography>
          )}
        </Box>
        <LinearProgress
          variant="determinate"
          value={percentage}
          sx={{ height: 8, borderRadius: 4 }}
          color="primary"
        />
      </Box>
    );
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            {widget.title}
          </Typography>
          
          {editMode && (
            <IconButton
              size="small"
              onClick={(e) => setMenuAnchor(e.currentTarget)}
            >
              <MoreVertRounded />
            </IconButton>
          )}
        </Box>

        <Box py={2}>
          {renderProgressIndicator()}
        </Box>

        <Box textAlign="center" mt={2}>
          <Typography variant="caption" color="text.secondary">
            Goal Progress
          </Typography>
        </Box>
      </CardContent>

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <EditRounded sx={{ mr: 1 }} />
          Configure
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <RefreshRounded sx={{ mr: 1 }} />
          Refresh
        </MenuItem>
        <MenuItem onClick={() => {
          onDelete();
          setMenuAnchor(null);
        }}>
          <DeleteRounded sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Card>
  );
};
