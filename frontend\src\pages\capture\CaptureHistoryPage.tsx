import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';

export const CaptureHistoryPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Capture', path: '/capture' },
      { label: 'History' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Capture History
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        View and manage all your captured content.
      </Typography>

      <Alert severity="info">
        <Typography variant="body2">
          Capture history interface is coming soon! This will include search, filtering, and detailed views of all your captured content.
        </Typography>
      </Alert>
    </Box>
  );
};
