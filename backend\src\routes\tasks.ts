import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';

import { authMiddleware, AuthenticatedRequest } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/errorHandler';
import { ValidationError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { advancedTaskService } from '@/services/advancedTaskService';

/**
 * Task Management API Routes
 * Includes basic task CRUD and advanced features like dependencies and analytics
 * TODO: Implement actual database operations and integrate with existing task system
 */

const router = express.Router();

// Rate limiting for task operations
const taskRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many task requests',
    message: 'Please wait before making more task requests',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// ============================================================================
// BASIC TASK ROUTES (TODO: Implement basic CRUD operations)
// ============================================================================

/**
 * @swagger
 * /api/tasks:
 *   get:
 *     summary: Get user's tasks
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [todo, in_progress, completed, cancelled]
 *         description: Filter by task status
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *         description: Filter by priority
 *       - in: query
 *         name: goalId
 *         schema:
 *           type: string
 *         description: Filter by goal ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of tasks to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Number of tasks to skip
 *     responses:
 *       200:
 *         description: Tasks retrieved successfully
 */
router.get('/',
  authMiddleware,
  taskRateLimiter,
  [
    query('status').optional().isIn(['todo', 'in_progress', 'completed', 'cancelled']).withMessage('Invalid status'),
    query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('Invalid priority'),
    query('goalId').optional().isUUID().withMessage('Invalid goal ID'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { status, priority, goalId, limit = 20, offset = 0 } = req.query;

    // TODO: Implement actual database query
    const mockTasks = [];

    res.json({
      success: true,
      data: {
        tasks: mockTasks,
        pagination: {
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          total: mockTasks.length,
        },
      },
    });
  })
);

// ============================================================================
// TASK DEPENDENCY ROUTES
// ============================================================================

/**
 * @swagger
 * /api/tasks/dependencies:
 *   post:
 *     summary: Create task dependency
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sourceTaskId:
 *                 type: string
 *                 description: Task that depends on target
 *               targetTaskId:
 *                 type: string
 *                 description: Task that is a dependency
 *               type:
 *                 type: string
 *                 enum: [finish_to_start, start_to_start, finish_to_finish, start_to_finish]
 *                 description: Dependency type
 *               lagHours:
 *                 type: number
 *                 description: Lag time in hours
 *               isHard:
 *                 type: boolean
 *                 description: Whether this is a hard dependency
 *             required:
 *               - sourceTaskId
 *               - targetTaskId
 *               - type
 *     responses:
 *       201:
 *         description: Dependency created successfully
 *       400:
 *         description: Validation error
 */
router.post('/dependencies',
  authMiddleware,
  [
    body('sourceTaskId').isUUID().withMessage('Invalid source task ID'),
    body('targetTaskId').isUUID().withMessage('Invalid target task ID'),
    body('type').isIn(['finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish']).withMessage('Invalid dependency type'),
    body('lagHours').optional().isNumeric().withMessage('Lag hours must be a number'),
    body('isHard').optional().isBoolean().withMessage('isHard must be a boolean'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { sourceTaskId, targetTaskId, type, lagHours = 0, isHard = true } = req.body;

    const dependency = await advancedTaskService.createTaskDependency({
      sourceTaskId,
      targetTaskId,
      type,
      lagHours,
      isHard,
    });

    logger.info(`Task dependency created: ${dependency.id} by user: ${req.user!.id}`);

    res.status(201).json({
      success: true,
      message: 'Task dependency created successfully',
      data: dependency,
    });
  })
);

/**
 * @swagger
 * /api/tasks/dependencies/graph:
 *   get:
 *     summary: Get task dependency graph
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: goalId
 *         schema:
 *           type: string
 *         description: Filter by goal ID
 *     responses:
 *       200:
 *         description: Dependency graph retrieved successfully
 */
router.get('/dependencies/graph',
  authMiddleware,
  [
    query('goalId').optional().isUUID().withMessage('Invalid goal ID'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { goalId } = req.query;

    const dependencyGraph = await advancedTaskService.getTaskDependencyGraph(userId, goalId as string);

    res.json({
      success: true,
      data: dependencyGraph,
    });
  })
);

// ============================================================================
// MILESTONE ROUTES
// ============================================================================

/**
 * @swagger
 * /api/tasks/milestones:
 *   post:
 *     summary: Create milestone
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               goalId:
 *                 type: string
 *                 description: Associated goal ID
 *               title:
 *                 type: string
 *                 description: Milestone title
 *               description:
 *                 type: string
 *                 description: Milestone description
 *               targetDate:
 *                 type: string
 *                 format: date-time
 *                 description: Target completion date
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 description: Milestone priority
 *               taskIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Associated task IDs
 *               successCriteria:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Success criteria
 *             required:
 *               - goalId
 *               - title
 *               - targetDate
 *     responses:
 *       201:
 *         description: Milestone created successfully
 */
router.post('/milestones',
  authMiddleware,
  [
    body('goalId').isUUID().withMessage('Invalid goal ID'),
    body('title').isLength({ min: 1, max: 255 }).withMessage('Title must be 1-255 characters'),
    body('description').optional().isLength({ max: 1000 }).withMessage('Description must be max 1000 characters'),
    body('targetDate').isISO8601().withMessage('Invalid target date'),
    body('priority').optional().isIn(['low', 'medium', 'high', 'critical']).withMessage('Invalid priority'),
    body('taskIds').optional().isArray().withMessage('Task IDs must be an array'),
    body('successCriteria').optional().isArray().withMessage('Success criteria must be an array'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const {
      goalId,
      title,
      description,
      targetDate,
      priority = 'medium',
      taskIds = [],
      successCriteria = [],
    } = req.body;

    const milestone = await advancedTaskService.createMilestone({
      userId,
      goalId,
      title,
      description,
      targetDate: new Date(targetDate),
      status: 'pending',
      priority,
      progress: 0,
      taskIds,
      successCriteria,
      metadata: {},
    });

    logger.info(`Milestone created: ${milestone.id} by user: ${userId}`);

    res.status(201).json({
      success: true,
      message: 'Milestone created successfully',
      data: milestone,
    });
  })
);

// ============================================================================
// PRIORITY MATRIX ROUTES
// ============================================================================

/**
 * @swagger
 * /api/tasks/priority-matrix:
 *   get:
 *     summary: Generate priority matrix
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: taskIds
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Specific task IDs to include
 *     responses:
 *       200:
 *         description: Priority matrix generated successfully
 */
router.get('/priority-matrix',
  authMiddleware,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const userId = req.user!.id;
    const taskIds = req.query.taskIds as string[] | undefined;

    const priorityMatrix = await advancedTaskService.generatePriorityMatrix(userId, taskIds);

    res.json({
      success: true,
      data: priorityMatrix,
    });
  })
);

// ============================================================================
// ANALYTICS ROUTES
// ============================================================================

/**
 * @swagger
 * /api/tasks/analytics:
 *   get:
 *     summary: Get task analytics
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Analytics start date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Analytics end date
 *     responses:
 *       200:
 *         description: Task analytics retrieved successfully
 */
router.get('/analytics',
  authMiddleware,
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();

    const analytics = await advancedTaskService.generateTaskAnalytics(userId, { start: startDate, end: endDate });

    res.json({
      success: true,
      data: analytics,
    });
  })
);

export default router;
