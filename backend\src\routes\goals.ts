import { Router, Response } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { authMiddleware, AuthenticatedRequest } from '@/middleware/auth';
import { asyncHandler, ValidationError, NotFoundError } from '@/middleware/errorHandler';
import { executeQuery, executeNeo4jQuery } from '@/config/database';
import { logger, logBusinessEvent } from '@/utils/logger';

const router = Router();

/**
 * @swagger
 * /api/goals:
 *   get:
 *     summary: Get user goals with optional filtering
 *     tags: [Goals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, paused, cancelled]
 *       - in: query
 *         name: parentId
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *     responses:
 *       200:
 *         description: Goals retrieved successfully
 */
router.get('/',
  authMiddleware,
  [
    query('status')
      .optional()
      .isIn(['active', 'completed', 'paused', 'cancelled'])
      .withMessage('Invalid status'),
    query('parentId')
      .optional()
      .isUUID()
      .withMessage('Parent ID must be a valid UUID'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Offset must be non-negative')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { status, parentId, limit = 50, offset = 0 } = req.query;

    let query = `
      SELECT g.*, 
             COUNT(sg.id) as subgoal_count,
             COUNT(t.id) as task_count,
             COALESCE(AVG(t.complexity_score), 0) as avg_task_complexity
      FROM goals g
      LEFT JOIN goals sg ON g.id = sg.parent_goal_id
      LEFT JOIN tasks t ON g.id = t.goal_id
      WHERE g.user_id = $1
    `;

    const params = [userId];
    let paramIndex = 2;

    if (status) {
      query += ` AND g.status = $${paramIndex++}`;
      params.push(status as string);
    }

    if (parentId) {
      query += ` AND g.parent_goal_id = $${paramIndex++}`;
      params.push(parentId as string);
    } else {
      // If no parentId specified, get top-level goals
      query += ` AND g.parent_goal_id IS NULL`;
    }

    query += `
      GROUP BY g.id
      ORDER BY g.priority DESC, g.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    params.push(limit.toString(), offset.toString());

    const result = await executeQuery(query, params);

    res.json({
      goals: result.rows,
      pagination: {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        total: result.rows.length
      }
    });
  })
);

/**
 * @swagger
 * /api/goals:
 *   post:
 *     summary: Create a new goal
 *     tags: [Goals]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 255
 *               description:
 *                 type: string
 *               parentGoalId:
 *                 type: string
 *                 format: uuid
 *               targetValue:
 *                 type: number
 *               unit:
 *                 type: string
 *                 maxLength: 50
 *               startDate:
 *                 type: string
 *                 format: date
 *               targetDate:
 *                 type: string
 *                 format: date
 *               priority:
 *                 type: integer
 *               color:
 *                 type: string
 *                 pattern: '^#[0-9A-Fa-f]{6}$'
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Goal created successfully
 */
router.post('/',
  authMiddleware,
  [
    body('title')
      .trim()
      .isLength({ min: 1, max: 255 })
      .withMessage('Title must be between 1 and 255 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('parentGoalId')
      .optional()
      .isUUID()
      .withMessage('Parent goal ID must be a valid UUID'),
    body('targetValue')
      .optional()
      .isNumeric()
      .withMessage('Target value must be a number'),
    body('unit')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Unit must be less than 50 characters'),
    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid date'),
    body('targetDate')
      .optional()
      .isISO8601()
      .withMessage('Target date must be a valid date'),
    body('priority')
      .optional()
      .isInt()
      .withMessage('Priority must be an integer'),
    body('color')
      .optional()
      .matches(/^#[0-9A-Fa-f]{6}$/)
      .withMessage('Color must be a valid hex color'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const {
      title,
      description,
      parentGoalId,
      targetValue,
      unit,
      startDate,
      targetDate,
      priority = 0,
      color = '#1976d2',
      metadata = {}
    } = req.body;

    // Validate parent goal exists if provided
    if (parentGoalId) {
      const parentQuery = `SELECT id FROM goals WHERE id = $1 AND user_id = $2`;
      const parentResult = await executeQuery(parentQuery, [parentGoalId, userId]);
      
      if (parentResult.rows.length === 0) {
        throw new ValidationError('Parent goal not found');
      }
    }

    // Create goal in PostgreSQL
    const insertQuery = `
      INSERT INTO goals (
        user_id, parent_goal_id, title, description, target_value, 
        unit, start_date, target_date, priority, color, metadata
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const result = await executeQuery(insertQuery, [
      userId,
      parentGoalId || null,
      title,
      description || null,
      targetValue || null,
      unit || null,
      startDate || null,
      targetDate || null,
      priority,
      color,
      JSON.stringify(metadata)
    ]);

    const goal = result.rows[0];

    // Create goal node in Neo4j
    try {
      const neo4jQuery = `
        CREATE (g:Goal {
          id: $goalId,
          userId: $userId,
          title: $title,
          description: $description,
          status: 'active',
          createdAt: datetime()
        })
        WITH g
        MATCH (u:User {id: $userId})
        MERGE (u)-[:OWNS]->(g)
        RETURN g
      `;

      await executeNeo4jQuery(neo4jQuery, {
        goalId: goal.id,
        userId,
        title,
        description: description || ''
      });

      // Create parent-child relationship in Neo4j if applicable
      if (parentGoalId) {
        const relationQuery = `
          MATCH (parent:Goal {id: $parentId})
          MATCH (child:Goal {id: $childId})
          MERGE (parent)-[:HAS_SUBGOAL]->(child)
        `;

        await executeNeo4jQuery(relationQuery, {
          parentId: parentGoalId,
          childId: goal.id
        });
      }
    } catch (error) {
      logger.warn('Failed to create goal in Neo4j:', error);
    }

    logBusinessEvent('Goal created', userId, {
      goalId: goal.id,
      title,
      hasParent: !!parentGoalId
    });

    res.status(201).json({
      message: 'Goal created successfully',
      goal
    });
  })
);

/**
 * @swagger
 * /api/goals/{id}:
 *   get:
 *     summary: Get a specific goal by ID
 *     tags: [Goals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Goal retrieved successfully
 *       404:
 *         description: Goal not found
 */
router.get('/:id',
  authMiddleware,
  [
    param('id')
      .isUUID()
      .withMessage('Goal ID must be a valid UUID')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const goalId = req.params.id;

    const query = `
      SELECT g.*,
             COUNT(DISTINCT sg.id) as subgoal_count,
             COUNT(DISTINCT t.id) as task_count,
             COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.id END) as completed_tasks,
             COALESCE(AVG(t.complexity_score), 0) as avg_task_complexity
      FROM goals g
      LEFT JOIN goals sg ON g.id = sg.parent_goal_id
      LEFT JOIN tasks t ON g.id = t.goal_id
      WHERE g.id = $1 AND g.user_id = $2
      GROUP BY g.id
    `;

    const result = await executeQuery(query, [goalId, userId]);

    if (result.rows.length === 0) {
      throw new NotFoundError('Goal not found');
    }

    const goal = result.rows[0];

    // Get subgoals
    const subgoalsQuery = `
      SELECT * FROM goals 
      WHERE parent_goal_id = $1 AND user_id = $2
      ORDER BY priority DESC, created_at ASC
    `;

    const subgoalsResult = await executeQuery(subgoalsQuery, [goalId, userId]);

    // Get related tasks
    const tasksQuery = `
      SELECT * FROM tasks 
      WHERE goal_id = $1 AND user_id = $2
      ORDER BY priority DESC, created_at DESC
      LIMIT 10
    `;

    const tasksResult = await executeQuery(tasksQuery, [goalId, userId]);

    res.json({
      goal: {
        ...goal,
        subgoals: subgoalsResult.rows,
        recentTasks: tasksResult.rows
      }
    });
  })
);

/**
 * @swagger
 * /api/goals/{id}:
 *   put:
 *     summary: Update a goal
 *     tags: [Goals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 255
 *               description:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, completed, paused, cancelled]
 *               targetValue:
 *                 type: number
 *               currentValue:
 *                 type: number
 *               unit:
 *                 type: string
 *               startDate:
 *                 type: string
 *                 format: date
 *               targetDate:
 *                 type: string
 *                 format: date
 *               priority:
 *                 type: integer
 *               color:
 *                 type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       200:
 *         description: Goal updated successfully
 *       404:
 *         description: Goal not found
 */
router.put('/:id',
  authMiddleware,
  [
    param('id')
      .isUUID()
      .withMessage('Goal ID must be a valid UUID'),
    body('title')
      .optional()
      .trim()
      .isLength({ min: 1, max: 255 })
      .withMessage('Title must be between 1 and 255 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('status')
      .optional()
      .isIn(['active', 'completed', 'paused', 'cancelled'])
      .withMessage('Invalid status'),
    body('targetValue')
      .optional()
      .isNumeric()
      .withMessage('Target value must be a number'),
    body('currentValue')
      .optional()
      .isNumeric()
      .withMessage('Current value must be a number'),
    body('unit')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Unit must be less than 50 characters'),
    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid date'),
    body('targetDate')
      .optional()
      .isISO8601()
      .withMessage('Target date must be a valid date'),
    body('priority')
      .optional()
      .isInt()
      .withMessage('Priority must be an integer'),
    body('color')
      .optional()
      .matches(/^#[0-9A-Fa-f]{6}$/)
      .withMessage('Color must be a valid hex color'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const goalId = req.params.id;

    // Check if goal exists
    const existsQuery = `SELECT id FROM goals WHERE id = $1 AND user_id = $2`;
    const existsResult = await executeQuery(existsQuery, [goalId, userId]);

    if (existsResult.rows.length === 0) {
      throw new NotFoundError('Goal not found');
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    const allowedFields = [
      'title', 'description', 'status', 'targetValue', 'currentValue',
      'unit', 'startDate', 'targetDate', 'priority', 'color', 'metadata'
    ];

    const fieldMapping: { [key: string]: string } = {
      targetValue: 'target_value',
      currentValue: 'current_value',
      startDate: 'start_date',
      targetDate: 'target_date'
    };

    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        const dbField = fieldMapping[key] || key.toLowerCase();
        updateFields.push(`${dbField} = $${paramIndex++}`);
        
        if (key === 'metadata') {
          updateValues.push(JSON.stringify(req.body[key]));
        } else {
          updateValues.push(req.body[key]);
        }
      }
    });

    if (updateFields.length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    // Add completion timestamp if status is being set to completed
    if (req.body.status === 'completed') {
      updateFields.push(`completed_at = NOW()`);
    }

    updateFields.push(`updated_at = NOW()`);
    updateValues.push(goalId, userId);

    const updateQuery = `
      UPDATE goals 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex++} AND user_id = $${paramIndex++}
      RETURNING *
    `;

    const result = await executeQuery(updateQuery, updateValues);
    const updatedGoal = result.rows[0];

    // Update Neo4j node
    try {
      const neo4jUpdateQuery = `
        MATCH (g:Goal {id: $goalId})
        SET g.title = $title,
            g.description = $description,
            g.status = $status,
            g.updatedAt = datetime()
        RETURN g
      `;

      await executeNeo4jQuery(neo4jUpdateQuery, {
        goalId,
        title: updatedGoal.title,
        description: updatedGoal.description || '',
        status: updatedGoal.status
      });
    } catch (error) {
      logger.warn('Failed to update goal in Neo4j:', error);
    }

    logBusinessEvent('Goal updated', userId, {
      goalId,
      updatedFields: Object.keys(req.body),
      newStatus: req.body.status
    });

    res.json({
      message: 'Goal updated successfully',
      goal: updatedGoal
    });
  })
);

export default router;
