# MindSync Frontend Environment Configuration

# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_WS_URL=http://localhost:5000

# Application Configuration
REACT_APP_NAME=MindSync
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Your second brain for productivity

# Feature Flags
REACT_APP_ENABLE_VOICE_CAPTURE=false
REACT_APP_ENABLE_IMAGE_CAPTURE=false
REACT_APP_ENABLE_EMAIL_PROCESSING=false
REACT_APP_ENABLE_WEB_CLIPPER=false
REACT_APP_ENABLE_ANALYTICS=false

# Development Configuration
REACT_APP_DEBUG=false
REACT_APP_LOG_LEVEL=info

# PWA Configuration
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_OFFLINE=true

# External Services (Optional)
REACT_APP_GOOGLE_ANALYTICS_ID=
REACT_APP_SENTRY_DSN=

# Theme Configuration
REACT_APP_DEFAULT_THEME=light
REACT_APP_ENABLE_DARK_MODE=true

# Performance Configuration
REACT_APP_ENABLE_LAZY_LOADING=true
REACT_APP_ENABLE_CODE_SPLITTING=true
