import axios from 'axios';
import { logger } from '@/utils/logger';
import { cache } from '@/config/database';
import { CustomError } from '@/middleware/errorHandler';

export interface NLPResult {
  entities: Entity[];
  suggestedTags: string[];
  sentiment: SentimentResult;
  topics: Topic[];
  actionItems: ActionItem[];
  confidence: number;
  language: string;
  summary?: string;
}

export interface Entity {
  name: string;
  type: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
}

export interface SentimentResult {
  label: 'positive' | 'negative' | 'neutral';
  score: number;
}

export interface Topic {
  name: string;
  confidence: number;
  keywords: string[];
}

export interface ActionItem {
  text: string;
  urgency: 'low' | 'medium' | 'high';
  estimatedDuration?: number;
  dueDate?: string;
}

class NLPService {
  private readonly huggingFaceApiUrl = 'https://api-inference.huggingface.co/models';
  private readonly apiKey = process.env.HUGGINGFACE_API_KEY;

  /**
   * Analyze text using multiple NLP models
   */
  async analyzeText(text: string): Promise<NLPResult> {
    try {
      // Check cache first
      const cacheKey = `nlp:${this.hashText(text)}`;
      const cached = await cache.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      logger.info('Analyzing text with NLP models');

      // Run multiple NLP tasks in parallel
      const [
        entities,
        sentiment,
        topics,
        language,
        summary
      ] = await Promise.allSettled([
        this.extractEntities(text),
        this.analyzeSentiment(text),
        this.extractTopics(text),
        this.detectLanguage(text),
        this.generateSummary(text)
      ]);

      // Extract action items using rule-based approach
      const actionItems = this.extractActionItems(text);
      
      // Generate tags from entities and topics
      const suggestedTags = this.generateTags(
        entities.status === 'fulfilled' ? entities.value : [],
        topics.status === 'fulfilled' ? topics.value : []
      );

      const result: NLPResult = {
        entities: entities.status === 'fulfilled' ? entities.value : [],
        suggestedTags,
        sentiment: sentiment.status === 'fulfilled' ? sentiment.value : { label: 'neutral', score: 0.5 },
        topics: topics.status === 'fulfilled' ? topics.value : [],
        actionItems,
        confidence: this.calculateOverallConfidence([entities, sentiment, topics]),
        language: language.status === 'fulfilled' ? language.value : 'en',
        summary: summary.status === 'fulfilled' ? summary.value : undefined
      };

      // Cache the result for 1 hour
      await cache.set(cacheKey, JSON.stringify(result), 3600);

      return result;

    } catch (error) {
      logger.error('Error analyzing text:', error);
      throw new CustomError('Failed to analyze text content');
    }
  }

  /**
   * Extract named entities from text
   */
  private async extractEntities(text: string): Promise<Entity[]> {
    try {
      const response = await this.callHuggingFaceAPI(
        'dbmdz/bert-large-cased-finetuned-conll03-english',
        { inputs: text }
      );

      if (!Array.isArray(response)) {
        return [];
      }

      return response.map((entity: any) => ({
        name: entity.word.replace(/^##/, ''), // Remove BERT subword prefix
        type: entity.entity_group || entity.entity,
        confidence: entity.score,
        startIndex: entity.start,
        endIndex: entity.end
      }));

    } catch (error) {
      logger.error('Error extracting entities:', error);
      return [];
    }
  }

  /**
   * Analyze sentiment of text
   */
  private async analyzeSentiment(text: string): Promise<SentimentResult> {
    try {
      const response = await this.callHuggingFaceAPI(
        'cardiffnlp/twitter-roberta-base-sentiment-latest',
        { inputs: text }
      );

      if (!Array.isArray(response) || response.length === 0) {
        return { label: 'neutral', score: 0.5 };
      }

      const result = response[0];
      const label = result.label.toLowerCase();
      
      return {
        label: label.includes('pos') ? 'positive' : 
               label.includes('neg') ? 'negative' : 'neutral',
        score: result.score
      };

    } catch (error) {
      logger.error('Error analyzing sentiment:', error);
      return { label: 'neutral', score: 0.5 };
    }
  }

  /**
   * Extract topics from text
   */
  private async extractTopics(text: string): Promise<Topic[]> {
    try {
      // Use zero-shot classification for topic extraction
      const candidateLabels = [
        'work', 'personal', 'health', 'finance', 'education', 'technology',
        'travel', 'food', 'entertainment', 'sports', 'politics', 'science',
        'business', 'productivity', 'goals', 'tasks', 'projects', 'ideas'
      ];

      const response = await this.callHuggingFaceAPI(
        'facebook/bart-large-mnli',
        {
          inputs: text,
          parameters: {
            candidate_labels: candidateLabels
          }
        }
      );

      if (!response.labels || !response.scores) {
        return [];
      }

      return response.labels
        .map((label: string, index: number) => ({
          name: label,
          confidence: response.scores[index],
          keywords: this.extractKeywordsForTopic(text, label)
        }))
        .filter((topic: Topic) => topic.confidence > 0.1)
        .slice(0, 5); // Top 5 topics

    } catch (error) {
      logger.error('Error extracting topics:', error);
      return [];
    }
  }

  /**
   * Detect language of text
   */
  private async detectLanguage(text: string): Promise<string> {
    try {
      // Simple language detection based on common words
      const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
      const words = text.toLowerCase().split(/\s+/);
      const englishWordCount = words.filter(word => englishWords.includes(word)).length;
      
      if (englishWordCount / words.length > 0.1) {
        return 'en';
      }

      // Fallback to API if needed
      const response = await this.callHuggingFaceAPI(
        'papluca/xlm-roberta-base-language-detection',
        { inputs: text }
      );

      if (Array.isArray(response) && response.length > 0) {
        return response[0].label;
      }

      return 'en'; // Default to English

    } catch (error) {
      logger.error('Error detecting language:', error);
      return 'en';
    }
  }

  /**
   * Generate summary of text
   */
  private async generateSummary(text: string): Promise<string | undefined> {
    try {
      if (text.length < 200) {
        return undefined; // Too short to summarize
      }

      const response = await this.callHuggingFaceAPI(
        'facebook/bart-large-cnn',
        {
          inputs: text,
          parameters: {
            max_length: 150,
            min_length: 30,
            do_sample: false
          }
        }
      );

      if (Array.isArray(response) && response.length > 0) {
        return response[0].summary_text;
      }

      return undefined;

    } catch (error) {
      logger.error('Error generating summary:', error);
      return undefined;
    }
  }

  /**
   * Extract action items using rule-based approach
   */
  private extractActionItems(text: string): ActionItem[] {
    const actionItems: ActionItem[] = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    const actionPatterns = [
      /(?:need to|should|must|have to|ought to|plan to|going to|will)\s+(.+)/i,
      /(?:todo|to do|action|task):\s*(.+)/i,
      /(?:remember to|don't forget to)\s+(.+)/i,
      /(?:schedule|book|call|email|contact|meet with|follow up)\s+(.+)/i
    ];

    const urgencyPatterns = [
      { pattern: /(?:urgent|asap|immediately|right away|critical)/i, level: 'high' as const },
      { pattern: /(?:soon|quickly|priority|important)/i, level: 'medium' as const },
      { pattern: /(?:later|eventually|when possible)/i, level: 'low' as const }
    ];

    sentences.forEach(sentence => {
      actionPatterns.forEach(pattern => {
        const match = sentence.match(pattern);
        if (match) {
          let urgency: 'low' | 'medium' | 'high' = 'medium';
          
          // Determine urgency
          urgencyPatterns.forEach(urgencyPattern => {
            if (urgencyPattern.pattern.test(sentence)) {
              urgency = urgencyPattern.level;
            }
          });

          // Estimate duration based on action type
          const estimatedDuration = this.estimateTaskDuration(match[1]);

          actionItems.push({
            text: match[1].trim(),
            urgency,
            estimatedDuration
          });
        }
      });
    });

    return actionItems.slice(0, 10); // Limit to 10 action items
  }

  /**
   * Estimate task duration based on content
   */
  private estimateTaskDuration(taskText: string): number {
    const text = taskText.toLowerCase();
    
    if (text.includes('call') || text.includes('email') || text.includes('message')) {
      return 15; // 15 minutes
    }
    
    if (text.includes('meeting') || text.includes('discuss') || text.includes('review')) {
      return 60; // 1 hour
    }
    
    if (text.includes('research') || text.includes('analyze') || text.includes('study')) {
      return 120; // 2 hours
    }
    
    if (text.includes('write') || text.includes('create') || text.includes('develop')) {
      return 90; // 1.5 hours
    }
    
    return 30; // Default 30 minutes
  }

  /**
   * Generate tags from entities and topics
   */
  private generateTags(entities: Entity[], topics: Topic[]): string[] {
    const tags = new Set<string>();

    // Add entity names as tags
    entities.forEach(entity => {
      if (entity.confidence > 0.7) {
        tags.add(entity.name.toLowerCase());
        tags.add(entity.type.toLowerCase());
      }
    });

    // Add topic names as tags
    topics.forEach(topic => {
      if (topic.confidence > 0.3) {
        tags.add(topic.name.toLowerCase());
        topic.keywords.forEach(keyword => tags.add(keyword.toLowerCase()));
      }
    });

    return Array.from(tags).slice(0, 15); // Limit to 15 tags
  }

  /**
   * Extract keywords for a specific topic
   */
  private extractKeywordsForTopic(text: string, topic: string): string[] {
    // Simple keyword extraction based on word frequency
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);

    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    });

    return Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word);
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(results: PromiseSettledResult<any>[]): number {
    const successfulResults = results.filter(r => r.status === 'fulfilled').length;
    return successfulResults / results.length;
  }

  /**
   * Call Hugging Face API
   */
  private async callHuggingFaceAPI(model: string, payload: any): Promise<any> {
    if (!this.apiKey) {
      throw new Error('Hugging Face API key not configured');
    }

    const response = await axios.post(
      `${this.huggingFaceApiUrl}/${model}`,
      payload,
      {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30 seconds timeout
      }
    );

    return response.data;
  }

  /**
   * Generate hash for text caching
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

export const nlpService = new NLPService();
