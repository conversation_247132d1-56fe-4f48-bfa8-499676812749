import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  Button,
  Box,
  LinearProgress,
  Avatar,
  Chip,
  Divider,
} from '@mui/material';
import {
  TrackChangesRounded,
  AddRounded,
  TrendingUpRounded,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

import type { Goal } from '@/store/api/goalsApi';
import { InlineLoading } from '@/components/LoadingScreen';

interface GoalsOverviewWidgetProps {
  goals: Goal[];
  loading: boolean;
}

const getProgressPercentage = (goal: Goal): number => {
  if (!goal.targetValue || goal.targetValue === 0) return 0;
  return Math.min((goal.currentValue / goal.targetValue) * 100, 100);
};

const getProgressColor = (percentage: number): 'primary' | 'success' | 'warning' | 'error' => {
  if (percentage >= 100) return 'success';
  if (percentage >= 75) return 'primary';
  if (percentage >= 50) return 'warning';
  return 'error';
};

const truncateText = (text: string, maxLength: number = 60): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const GoalsOverviewWidget: React.FC<GoalsOverviewWidgetProps> = ({
  goals,
  loading,
}) => {
  const navigate = useNavigate();

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} gutterBottom>
            Active Goals
          </Typography>
          <InlineLoading message="Loading goals..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Active Goals
          </Typography>
          <Button
            size="small"
            endIcon={<TrackChangesRounded />}
            onClick={() => navigate('/goals')}
          >
            View All
          </Button>
        </Box>

        {goals.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={4}
            textAlign="center"
          >
            <Avatar
              sx={{
                width: 64,
                height: 64,
                bgcolor: 'grey.100',
                color: 'grey.400',
                mb: 2,
              }}
            >
              <TrackChangesRounded fontSize="large" />
            </Avatar>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              No active goals
            </Typography>
            <Typography variant="caption" color="text.secondary" mb={2}>
              Set your first goal to start tracking progress!
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddRounded />}
              onClick={() => navigate('/goals')}
            >
              Create Goal
            </Button>
          </Box>
        ) : (
          <>
            <List disablePadding>
              {goals.map((goal, index) => {
                const progress = getProgressPercentage(goal);
                const progressColor = getProgressColor(progress);

                return (
                  <React.Fragment key={goal.id}>
                    <ListItem
                      disablePadding
                      sx={{
                        py: 1.5,
                        cursor: 'pointer',
                        borderRadius: 1,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                      onClick={() => navigate(`/goals/${goal.id}`)}
                    >
                      <ListItemText
                        primary={
                          <Box mb={1}>
                            <Typography variant="body2" fontWeight={500} gutterBottom>
                              {truncateText(goal.title)}
                            </Typography>
                            
                            <Box display="flex" alignItems="center" gap={1} mb={1}>
                              <Chip
                                label={goal.status}
                                size="small"
                                color={goal.status === 'completed' ? 'success' : 'primary'}
                                variant="outlined"
                                sx={{ height: 20, fontSize: '0.625rem' }}
                              />
                              {goal.priority > 0 && (
                                <Chip
                                  label={`Priority: ${goal.priority}`}
                                  size="small"
                                  color="secondary"
                                  variant="outlined"
                                  sx={{ height: 20, fontSize: '0.625rem' }}
                                />
                              )}
                            </Box>

                            {goal.targetValue && (
                              <Box>
                                <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                                  <Typography variant="caption" color="text.secondary">
                                    Progress
                                  </Typography>
                                  <Typography variant="caption" color={`${progressColor}.main`} fontWeight={600}>
                                    {goal.currentValue}/{goal.targetValue} {goal.unit || ''}
                                  </Typography>
                                </Box>
                                <LinearProgress
                                  variant="determinate"
                                  value={progress}
                                  color={progressColor}
                                  sx={{ height: 6, borderRadius: 3 }}
                                />
                              </Box>
                            )}
                          </Box>
                        }
                        secondary={
                          <Box display="flex" alignItems="center" justifyContent="space-between">
                            <Typography variant="caption" color="text.secondary">
                              {formatDistanceToNow(new Date(goal.createdAt), { addSuffix: true })}
                            </Typography>
                            {goal.targetDate && (
                              <Typography variant="caption" color="text.secondary">
                                Due: {new Date(goal.targetDate).toLocaleDateString()}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    
                    {index < goals.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                );
              })}
            </List>

            <Box mt={2} display="flex" gap={1}>
              <Button
                fullWidth
                variant="outlined"
                size="small"
                startIcon={<AddRounded />}
                onClick={() => navigate('/goals')}
              >
                New Goal
              </Button>
              <Button
                fullWidth
                variant="outlined"
                size="small"
                startIcon={<TrendingUpRounded />}
                onClick={() => navigate('/analytics')}
              >
                Analytics
              </Button>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};
