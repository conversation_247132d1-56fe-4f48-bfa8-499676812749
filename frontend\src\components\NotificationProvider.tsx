import React, { createContext, useContext, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ert, AlertT<PERSON>le, Slide, SlideProps } from '@mui/material';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { removeNotification, selectNotifications } from '@/store/slices/uiSlice';

interface NotificationContextType {
  showNotification: (
    message: string,
    type?: 'success' | 'error' | 'warning' | 'info',
    options?: {
      title?: string;
      duration?: number;
      persistent?: boolean;
    }
  ) => void;
  showSuccess: (message: string, title?: string) => void;
  showError: (message: string, title?: string) => void;
  showWarning: (message: string, title?: string) => void;
  showInfo: (message: string, title?: string) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

function SlideTransition(props: SlideProps) {
  return <Slide {...props} direction="up" />;
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector(selectNotifications);

  const showNotification = useCallback(
    (
      message: string,
      type: 'success' | 'error' | 'warning' | 'info' = 'info',
      options: {
        title?: string;
        duration?: number;
        persistent?: boolean;
      } = {}
    ) => {
      dispatch({
        type: 'ui/addNotification',
        payload: {
          message,
          type,
          title: options.title,
          duration: options.duration || (type === 'error' ? 6000 : 4000),
          persistent: options.persistent || false,
        },
      });
    },
    [dispatch]
  );

  const showSuccess = useCallback(
    (message: string, title?: string) => {
      showNotification(message, 'success', { title });
    },
    [showNotification]
  );

  const showError = useCallback(
    (message: string, title?: string) => {
      showNotification(message, 'error', { title, duration: 6000 });
    },
    [showNotification]
  );

  const showWarning = useCallback(
    (message: string, title?: string) => {
      showNotification(message, 'warning', { title });
    },
    [showNotification]
  );

  const showInfo = useCallback(
    (message: string, title?: string) => {
      showNotification(message, 'info', { title });
    },
    [showNotification]
  );

  const handleClose = useCallback(
    (notificationId: string) => {
      dispatch(removeNotification(notificationId));
    },
    [dispatch]
  );

  const contextValue: NotificationContextType = {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Render notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={notification.persistent ? null : notification.duration}
          onClose={() => handleClose(notification.id)}
          TransitionComponent={SlideTransition}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          sx={{
            mb: index * 7, // Stack notifications
          }}
        >
          <Alert
            onClose={() => handleClose(notification.id)}
            severity={notification.type}
            variant="filled"
            sx={{
              minWidth: 300,
              maxWidth: 500,
            }}
          >
            {notification.title && (
              <AlertTitle>{notification.title}</AlertTitle>
            )}
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </NotificationContext.Provider>
  );
};

export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};
