import { Router, Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { authMiddleware, AuthenticatedRequest } from '@/middleware/auth';
import { uploadRateLimiter } from '@/middleware/rateLimiter';
import { asyncHandler, ValidationError, CustomError } from '@/middleware/errorHandler';
import { captureService } from '@/services/captureService';
import { logger, logBusinessEvent } from '@/utils/logger';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'captures');
    await fs.mkdir(uploadDir, { recursive: true });
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `capture-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/bmp': ['.bmp'],
      'image/tiff': ['.tiff', '.tif'],
      'image/webp': ['.webp'],
      'audio/wav': ['.wav'],
      'audio/mpeg': ['.mp3'],
      'audio/mp4': ['.m4a'],
      'audio/ogg': ['.ogg'],
      'audio/flac': ['.flac'],
      'audio/webm': ['.webm']
    };

    const isAllowed = Object.keys(allowedTypes).includes(file.mimetype);
    const ext = path.extname(file.originalname).toLowerCase();
    const validExt = allowedTypes[file.mimetype as keyof typeof allowedTypes]?.includes(ext);

    if (isAllowed && validExt) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`));
    }
  }
});

/**
 * @swagger
 * /api/capture/text:
 *   post:
 *     summary: Capture and process text input
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 10000
 *               metadata:
 *                 type: object
 *               sourceUrl:
 *                 type: string
 *                 format: uri
 *     responses:
 *       201:
 *         description: Text captured and processed successfully
 *       400:
 *         description: Validation error
 */
router.post('/text',
  authMiddleware,
  [
    body('content')
      .trim()
      .isLength({ min: 1, max: 10000 })
      .withMessage('Content must be between 1 and 10000 characters'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object'),
    body('sourceUrl')
      .optional()
      .isURL()
      .withMessage('Source URL must be a valid URL')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { content, metadata, sourceUrl } = req.body;
    const userId = req.user!.id;

    const captureInput = {
      userId,
      type: 'text' as const,
      content,
      metadata,
      sourceUrl
    };

    const result = await captureService.processCapture(captureInput);

    logBusinessEvent('Text capture processed', userId, {
      captureId: result.id,
      contentLength: content.length,
      entitiesFound: result.extractedEntities.length,
      tagsGenerated: result.suggestedTags.length
    });

    res.status(201).json({
      message: 'Text captured and processed successfully',
      capture: result
    });
  })
);

/**
 * @swagger
 * /api/capture/voice:
 *   post:
 *     summary: Capture and process voice input
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: audio
 *         type: file
 *         required: true
 *         description: Audio file to transcribe
 *       - in: formData
 *         name: language
 *         type: string
 *         description: Language code for transcription
 *       - in: formData
 *         name: enhanceAudio
 *         type: boolean
 *         description: Whether to enhance audio quality
 *       - in: formData
 *         name: metadata
 *         type: string
 *         description: JSON metadata object
 *     responses:
 *       201:
 *         description: Voice captured and processed successfully
 *       400:
 *         description: Validation error
 */
router.post('/voice',
  authMiddleware,
  uploadRateLimiter,
  upload.single('audio'),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    if (!req.file) {
      throw new ValidationError('Audio file is required');
    }

    const userId = req.user!.id;
    const { language, enhanceAudio, metadata } = req.body;

    let parsedMetadata = {};
    if (metadata) {
      try {
        parsedMetadata = JSON.parse(metadata);
      } catch (error) {
        throw new ValidationError('Invalid metadata JSON');
      }
    }

    const captureInput = {
      userId,
      type: 'voice' as const,
      content: '', // Will be filled by speech-to-text
      filePath: req.file.path,
      metadata: {
        ...parsedMetadata,
        originalFilename: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        language,
        enhanceAudio: enhanceAudio === 'true'
      }
    };

    try {
      const result = await captureService.processCapture(captureInput);

      logBusinessEvent('Voice capture processed', userId, {
        captureId: result.id,
        filename: req.file.originalname,
        fileSize: req.file.size,
        transcribedLength: result.processedContent.length
      });

      res.status(201).json({
        message: 'Voice captured and processed successfully',
        capture: result
      });
    } finally {
      // Clean up uploaded file
      try {
        await fs.unlink(req.file.path);
      } catch (error) {
        logger.warn('Failed to clean up uploaded file:', error);
      }
    }
  })
);

/**
 * @swagger
 * /api/capture/image:
 *   post:
 *     summary: Capture and process image input with OCR
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: image
 *         type: file
 *         required: true
 *         description: Image file to process with OCR
 *       - in: formData
 *         name: language
 *         type: string
 *         description: Language code for OCR
 *       - in: formData
 *         name: preprocessImage
 *         type: boolean
 *         description: Whether to preprocess image for better OCR
 *       - in: formData
 *         name: metadata
 *         type: string
 *         description: JSON metadata object
 *     responses:
 *       201:
 *         description: Image captured and processed successfully
 *       400:
 *         description: Validation error
 */
router.post('/image',
  authMiddleware,
  uploadRateLimiter,
  upload.single('image'),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    if (!req.file) {
      throw new ValidationError('Image file is required');
    }

    const userId = req.user!.id;
    const { language, preprocessImage, metadata } = req.body;

    let parsedMetadata = {};
    if (metadata) {
      try {
        parsedMetadata = JSON.parse(metadata);
      } catch (error) {
        throw new ValidationError('Invalid metadata JSON');
      }
    }

    const captureInput = {
      userId,
      type: 'image' as const,
      content: '', // Will be filled by OCR
      filePath: req.file.path,
      metadata: {
        ...parsedMetadata,
        originalFilename: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        language: language || 'eng',
        preprocessImage: preprocessImage === 'true'
      }
    };

    try {
      const result = await captureService.processCapture(captureInput);

      logBusinessEvent('Image capture processed', userId, {
        captureId: result.id,
        filename: req.file.originalname,
        fileSize: req.file.size,
        extractedTextLength: result.processedContent.length
      });

      res.status(201).json({
        message: 'Image captured and processed successfully',
        capture: result
      });
    } finally {
      // Clean up uploaded file
      try {
        await fs.unlink(req.file.path);
      } catch (error) {
        logger.warn('Failed to clean up uploaded file:', error);
      }
    }
  })
);

/**
 * @swagger
 * /api/capture/email:
 *   post:
 *     summary: Process email content for actionable items
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 minLength: 1
 *               subject:
 *                 type: string
 *               sender:
 *                 type: string
 *               recipient:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date-time
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Email processed successfully
 *       400:
 *         description: Validation error
 */
router.post('/email',
  authMiddleware,
  [
    body('content')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Email content is required'),
    body('subject')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Subject must be less than 500 characters'),
    body('sender')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Sender must be less than 255 characters'),
    body('recipient')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Recipient must be less than 255 characters'),
    body('date')
      .optional()
      .isISO8601()
      .withMessage('Date must be a valid ISO 8601 date')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { content, subject, sender, recipient, date, metadata } = req.body;
    const userId = req.user!.id;

    const captureInput = {
      userId,
      type: 'email' as const,
      content,
      metadata: {
        ...metadata,
        subject,
        sender,
        recipient,
        date
      }
    };

    const result = await captureService.processCapture(captureInput);

    logBusinessEvent('Email capture processed', userId, {
      captureId: result.id,
      subject,
      sender,
      contentLength: content.length
    });

    res.status(201).json({
      message: 'Email processed successfully',
      capture: result
    });
  })
);

/**
 * @swagger
 * /api/capture/web-clip:
 *   post:
 *     summary: Capture and process web page content
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *               - sourceUrl
 *             properties:
 *               content:
 *                 type: string
 *                 minLength: 1
 *               sourceUrl:
 *                 type: string
 *                 format: uri
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Web clip processed successfully
 *       400:
 *         description: Validation error
 */
router.post('/web-clip',
  authMiddleware,
  [
    body('content')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Web page content is required'),
    body('sourceUrl')
      .isURL()
      .withMessage('Valid source URL is required'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { content, sourceUrl, metadata } = req.body;
    const userId = req.user!.id;

    const captureInput = {
      userId,
      type: 'web_clip' as const,
      content,
      sourceUrl,
      metadata
    };

    const result = await captureService.processCapture(captureInput);

    logBusinessEvent('Web clip capture processed', userId, {
      captureId: result.id,
      sourceUrl,
      contentLength: content.length
    });

    res.status(201).json({
      message: 'Web clip processed successfully',
      capture: result
    });
  })
);

/**
 * @swagger
 * /api/capture/history:
 *   get:
 *     summary: Get capture history for the authenticated user
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [text, voice, image, email, web_clip]
 *     responses:
 *       200:
 *         description: Capture history retrieved successfully
 */
router.get('/history',
  authMiddleware,
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Offset must be a non-negative integer'),
    query('type')
      .optional()
      .isIn(['text', 'voice', 'image', 'email', 'web_clip'])
      .withMessage('Invalid capture type')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;
    const type = req.query.type as string;

    const captures = await captureService.getCaptureHistory(userId, limit, offset, type);

    res.json({
      captures,
      pagination: {
        limit,
        offset,
        total: captures.length
      }
    });
  })
);

/**
 * @swagger
 * /api/capture/search:
 *   get:
 *     summary: Search captures by content
 *     tags: [Capture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 */
router.get('/search',
  authMiddleware,
  [
    query('q')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search query is required'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const searchTerm = req.query.q as string;
    const limit = parseInt(req.query.limit as string) || 20;

    const results = await captureService.searchCaptures(userId, searchTerm, limit);

    res.json({
      query: searchTerm,
      results,
      total: results.length
    });
  })
);

export default router;
