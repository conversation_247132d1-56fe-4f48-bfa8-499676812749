import React from 'react';
import { useLocation, Link as RouterLink } from 'react-router-dom';
import {
  Breadcrumbs as MuiBreadcrumbs,
  Link,
  Typography,
  Box,
} from '@mui/material';
import { NavigateNextRounded, HomeRounded } from '@mui/icons-material';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactNode;
}

const routeLabels: Record<string, string> = {
  dashboard: 'Dashboard',
  capture: 'Quick Capture',
  history: 'History',
  goals: 'Goals',
  tasks: 'Tasks',
  analytics: 'Analytics',
  templates: 'Templates',
  profile: 'Profile',
  settings: 'Settings',
};

export const Breadcrumbs: React.FC = () => {
  const location = useLocation();

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    
    if (pathSegments.length === 0 || (pathSegments.length === 1 && pathSegments[0] === 'dashboard')) {
      return [{ label: 'Dashboard', icon: <HomeRounded fontSize="small" /> }];
    }

    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Dashboard', path: '/dashboard', icon: <HomeRounded fontSize="small" /> }
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
      
      // Don't add link for the last segment (current page)
      if (index === pathSegments.length - 1) {
        breadcrumbs.push({ label });
      } else {
        breadcrumbs.push({ label, path: currentPath });
      }
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        {breadcrumbs[0]?.icon}
        <Typography variant="h6" fontWeight={600}>
          {breadcrumbs[0]?.label}
        </Typography>
      </Box>
    );
  }

  return (
    <MuiBreadcrumbs
      separator={<NavigateNextRounded fontSize="small" />}
      aria-label="breadcrumb"
    >
      {breadcrumbs.map((breadcrumb, index) => {
        const isLast = index === breadcrumbs.length - 1;
        
        if (isLast) {
          return (
            <Box key={breadcrumb.label} display="flex" alignItems="center" gap={1}>
              {breadcrumb.icon}
              <Typography variant="h6" fontWeight={600} color="text.primary">
                {breadcrumb.label}
              </Typography>
            </Box>
          );
        }

        return (
          <Link
            key={breadcrumb.label}
            component={RouterLink}
            to={breadcrumb.path!}
            underline="hover"
            color="inherit"
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              '&:hover': {
                color: 'primary.main',
              },
            }}
          >
            {breadcrumb.icon}
            {breadcrumb.label}
          </Link>
        );
      })}
    </MuiBreadcrumbs>
  );
};
