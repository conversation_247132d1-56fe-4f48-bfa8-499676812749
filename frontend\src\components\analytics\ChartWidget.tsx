import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton,
  Menu,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import {
  MoreVertRounded,
  EditRounded,
  DeleteRounded,
  RefreshRounded,
  ShowChartRounded,
} from '@mui/icons-material';

import type { DashboardWidget } from '@/types/analytics';

/**
 * Chart Widget Component - Displays data visualizations
 * TODO: Integrate with actual charting library (Chart.js, Recharts, or similar)
 */

interface ChartWidgetProps {
  widget: DashboardWidget;
  editMode: boolean;
  onUpdate: (updates: Partial<DashboardWidget>) => void;
  onDelete: () => void;
}

export const ChartWidget: React.FC<ChartWidgetProps> = ({
  widget,
  editMode,
  onUpdate,
  onDelete,
}) => {
  const [loading, setLoading] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  // Mock chart data loading
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            {widget.title}
          </Typography>
          
          {editMode && (
            <IconButton
              size="small"
              onClick={(e) => setMenuAnchor(e.currentTarget)}
            >
              <MoreVertRounded />
            </IconButton>
          )}
        </Box>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height={200}>
            <CircularProgress />
          </Box>
        ) : (
          <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" height={200}>
            <ShowChartRounded sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Chart visualization will be implemented here
            </Typography>
            <Typography variant="caption" color="text.secondary" mt={1}>
              TODO: Integrate with charting library
            </Typography>
          </Box>
        )}
      </CardContent>

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <EditRounded sx={{ mr: 1 }} />
          Configure
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <RefreshRounded sx={{ mr: 1 }} />
          Refresh
        </MenuItem>
        <MenuItem onClick={() => {
          onDelete();
          setMenuAnchor(null);
        }}>
          <DeleteRounded sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Card>
  );
};
