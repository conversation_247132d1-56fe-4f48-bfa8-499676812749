{"name": "mindsync", "version": "1.0.0", "description": "Your second brain for productivity - A comprehensive productivity application", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "start:prod": "NODE_ENV=production cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:integration": "cd backend && npm run test:integration", "test:e2e": "cd frontend && npm run test:e2e", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:down": "docker-compose down", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules", "postinstall": "npm run install:all"}, "keywords": ["productivity", "second-brain", "knowledge-management", "task-management", "goal-tracking", "ai-powered", "graph-database", "react", "nodejs", "typescript"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/mindsync.git"}, "bugs": {"url": "https://github.com/HectorTa1989/mindsync/issues"}, "homepage": "https://github.com/HectorTa1989/mindsync#readme", "devDependencies": {"concurrently": "^7.6.0", "husky": "^8.0.3", "lint-staged": "^13.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}