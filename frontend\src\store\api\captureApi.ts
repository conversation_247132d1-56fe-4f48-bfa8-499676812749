import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';

export interface CaptureEntity {
  name: string;
  type: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
}

export interface ProcessedCapture {
  id: string;
  userId: string;
  type: 'text' | 'voice' | 'image' | 'email' | 'web_clip';
  originalContent: string;
  processedContent: string;
  extractedEntities: CaptureEntity[];
  suggestedTags: string[];
  relatedNodes: any[];
  confidence: number;
  metadata: Record<string, any>;
  createdAt: string;
}

export interface TextCaptureRequest {
  content: string;
  metadata?: Record<string, any>;
  sourceUrl?: string;
}

export interface EmailCaptureRequest {
  content: string;
  subject?: string;
  sender?: string;
  recipient?: string;
  date?: string;
  metadata?: Record<string, any>;
}

export interface WebClipCaptureRequest {
  content: string;
  sourceUrl: string;
  metadata?: Record<string, any>;
}

export interface CaptureHistoryParams {
  limit?: number;
  offset?: number;
  type?: 'text' | 'voice' | 'image' | 'email' | 'web_clip';
}

export interface CaptureHistoryResponse {
  captures: ProcessedCapture[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
  };
}

export interface CaptureSearchParams {
  q: string;
  limit?: number;
}

export interface CaptureSearchResponse {
  query: string;
  results: ProcessedCapture[];
  total: number;
}

export interface CaptureResponse {
  message: string;
  capture: ProcessedCapture;
}

export const captureApi = createApi({
  reducerPath: 'captureApi',
  baseQuery: fetchBaseQuery({
    baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
    credentials: 'include',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Capture'],
  endpoints: (builder) => ({
    captureText: builder.mutation<CaptureResponse, TextCaptureRequest>({
      query: (data) => ({
        url: '/capture/text',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Capture'],
    }),
    
    captureVoice: builder.mutation<CaptureResponse, FormData>({
      query: (formData) => ({
        url: '/capture/voice',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['Capture'],
    }),
    
    captureImage: builder.mutation<CaptureResponse, FormData>({
      query: (formData) => ({
        url: '/capture/image',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['Capture'],
    }),
    
    captureEmail: builder.mutation<CaptureResponse, EmailCaptureRequest>({
      query: (data) => ({
        url: '/capture/email',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Capture'],
    }),
    
    captureWebClip: builder.mutation<CaptureResponse, WebClipCaptureRequest>({
      query: (data) => ({
        url: '/capture/web-clip',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Capture'],
    }),
    
    getCaptureHistory: builder.query<CaptureHistoryResponse, CaptureHistoryParams>({
      query: (params) => ({
        url: '/capture/history',
        params,
      }),
      providesTags: ['Capture'],
    }),
    
    searchCaptures: builder.query<CaptureSearchResponse, CaptureSearchParams>({
      query: (params) => ({
        url: '/capture/search',
        params,
      }),
      providesTags: ['Capture'],
    }),
  }),
});

export const {
  useCaptureTextMutation,
  useCaptureVoiceMutation,
  useCaptureImageMutation,
  useCaptureEmailMutation,
  useCaptureWebClipMutation,
  useGetCaptureHistoryQuery,
  useSearchCapturesQuery,
} = captureApi;
