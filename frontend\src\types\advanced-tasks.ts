/**
 * Advanced Task and Goal Management Types for MindSync
 * Extends existing task management with hierarchy, dependencies, and analytics
 */

import type { Task, Goal } from '@/store/api/tasksApi';

// ============================================================================
// TASK DEPENDENCY TYPES
// ============================================================================

export interface TaskDependency {
  /** Unique dependency ID */
  id: string;
  /** Source task ID (depends on target) */
  sourceTaskId: string;
  /** Target task ID (dependency) */
  targetTaskId: string;
  /** Dependency type */
  type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish';
  /** Lag time in hours (can be negative for lead time) */
  lagHours: number;
  /** Whether this is a hard or soft dependency */
  isHard: boolean;
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

export interface TaskDependencyGraph {
  /** All tasks in the graph */
  tasks: Task[];
  /** All dependencies */
  dependencies: TaskDependency[];
  /** Calculated critical path */
  criticalPath: string[];
  /** Tasks that are blocking others */
  blockingTasks: string[];
  /** Tasks that are blocked by others */
  blockedTasks: string[];
  /** Circular dependency warnings */
  circularDependencies: string[][];
}

// ============================================================================
// MILESTONE TYPES
// ============================================================================

export interface Milestone {
  /** Unique milestone ID */
  id: string;
  /** User ID */
  userId: string;
  /** Associated goal ID */
  goalId: string;
  /** Milestone title */
  title: string;
  /** Milestone description */
  description?: string;
  /** Target completion date */
  targetDate: Date;
  /** Actual completion date */
  completedAt?: Date;
  /** Milestone status */
  status: 'pending' | 'in_progress' | 'completed' | 'overdue' | 'cancelled';
  /** Priority level */
  priority: 'low' | 'medium' | 'high' | 'critical';
  /** Progress percentage (0-100) */
  progress: number;
  /** Associated task IDs */
  taskIds: string[];
  /** Success criteria */
  successCriteria: string[];
  /** Milestone metadata */
  metadata: Record<string, any>;
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

export interface MilestoneProgress {
  /** Milestone ID */
  milestoneId: string;
  /** Current progress percentage */
  progress: number;
  /** Completed tasks count */
  completedTasks: number;
  /** Total tasks count */
  totalTasks: number;
  /** Days until target date */
  daysUntilTarget: number;
  /** Whether milestone is on track */
  isOnTrack: boolean;
  /** Risk level */
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  /** Completion prediction */
  predictedCompletion?: Date;
}

// ============================================================================
// GOAL HIERARCHY TYPES
// ============================================================================

export interface GoalHierarchy {
  /** Goal ID */
  id: string;
  /** Parent goal ID */
  parentId?: string;
  /** Child goal IDs */
  childIds: string[];
  /** Goal level in hierarchy (0 = root) */
  level: number;
  /** Full path from root */
  path: string[];
  /** Whether this is a leaf node */
  isLeaf: boolean;
  /** Number of descendants */
  descendantCount: number;
}

export interface GoalTree {
  /** Root goals */
  roots: Goal[];
  /** Hierarchy mapping */
  hierarchy: Map<string, GoalHierarchy>;
  /** Goal lookup by ID */
  goals: Map<string, Goal>;
  /** Maximum depth */
  maxDepth: number;
  /** Total goal count */
  totalCount: number;
}

// ============================================================================
// PRIORITY MATRIX TYPES
// ============================================================================

export interface PriorityMatrix {
  /** Matrix quadrants */
  quadrants: {
    /** Urgent and Important (Do First) */
    urgent_important: Task[];
    /** Not Urgent but Important (Schedule) */
    not_urgent_important: Task[];
    /** Urgent but Not Important (Delegate) */
    urgent_not_important: Task[];
    /** Not Urgent and Not Important (Eliminate) */
    not_urgent_not_important: Task[];
  };
  /** Matrix metadata */
  metadata: {
    /** Total tasks in matrix */
    totalTasks: number;
    /** Last updated */
    lastUpdated: Date;
    /** User preferences for urgency/importance scoring */
    scoringPreferences: {
      dueDateWeight: number;
      priorityWeight: number;
      goalAlignmentWeight: number;
      complexityWeight: number;
    };
  };
}

export interface TaskPriorityScore {
  /** Task ID */
  taskId: string;
  /** Overall priority score (0-100) */
  overallScore: number;
  /** Urgency score (0-100) */
  urgencyScore: number;
  /** Importance score (0-100) */
  importanceScore: number;
  /** Score breakdown */
  breakdown: {
    dueDateScore: number;
    priorityScore: number;
    goalAlignmentScore: number;
    complexityScore: number;
    dependencyScore: number;
  };
  /** Recommended quadrant */
  recommendedQuadrant: 'urgent_important' | 'not_urgent_important' | 'urgent_not_important' | 'not_urgent_not_important';
}

// ============================================================================
// RECURRING TASK TYPES
// ============================================================================

export interface RecurringTaskPattern {
  /** Pattern ID */
  id: string;
  /** Pattern type */
  type: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
  /** Interval (e.g., every 2 days) */
  interval: number;
  /** Days of week (for weekly patterns) */
  daysOfWeek?: number[]; // 0 = Sunday, 1 = Monday, etc.
  /** Day of month (for monthly patterns) */
  dayOfMonth?: number;
  /** Month and day (for yearly patterns) */
  monthAndDay?: { month: number; day: number };
  /** Custom cron expression */
  cronExpression?: string;
  /** Pattern start date */
  startDate: Date;
  /** Pattern end date (optional) */
  endDate?: Date;
  /** Maximum occurrences */
  maxOccurrences?: number;
  /** Skip weekends */
  skipWeekends: boolean;
  /** Skip holidays */
  skipHolidays: boolean;
}

export interface RecurringTask {
  /** Recurring task ID */
  id: string;
  /** User ID */
  userId: string;
  /** Template task (used to create instances) */
  templateTask: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>;
  /** Recurrence pattern */
  pattern: RecurringTaskPattern;
  /** Status */
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  /** Generated task instances */
  instances: {
    taskId: string;
    scheduledDate: Date;
    status: 'pending' | 'created' | 'completed' | 'skipped';
  }[];
  /** Next scheduled date */
  nextScheduledDate?: Date;
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

// ============================================================================
// PROGRESS ANALYTICS TYPES
// ============================================================================

export interface TaskAnalytics {
  /** Time period for analytics */
  period: {
    start: Date;
    end: Date;
  };
  /** Task completion metrics */
  completion: {
    totalTasks: number;
    completedTasks: number;
    completionRate: number;
    averageCompletionTime: number; // hours
    onTimeCompletions: number;
    overdueCompletions: number;
  };
  /** Productivity metrics */
  productivity: {
    tasksPerDay: number;
    focusTime: number; // hours
    distractionTime: number; // hours
    productivityScore: number; // 0-100
    peakProductivityHours: number[];
  };
  /** Goal alignment metrics */
  goalAlignment: {
    alignedTasks: number;
    unalignedTasks: number;
    alignmentScore: number; // 0-100
    topGoalContributions: {
      goalId: string;
      goalTitle: string;
      taskCount: number;
      completionRate: number;
    }[];
  };
  /** Trend analysis */
  trends: {
    completionTrend: 'improving' | 'declining' | 'stable';
    productivityTrend: 'improving' | 'declining' | 'stable';
    complexityTrend: 'increasing' | 'decreasing' | 'stable';
    burnoutRisk: 'low' | 'medium' | 'high';
  };
}

export interface GoalAnalytics {
  /** Goal ID */
  goalId: string;
  /** Time period */
  period: {
    start: Date;
    end: Date;
  };
  /** Progress metrics */
  progress: {
    currentProgress: number; // 0-100
    targetProgress: number; // 0-100
    progressVelocity: number; // progress per day
    estimatedCompletion: Date;
    isOnTrack: boolean;
  };
  /** Task metrics */
  tasks: {
    totalTasks: number;
    completedTasks: number;
    inProgressTasks: number;
    blockedTasks: number;
    averageTaskComplexity: number;
  };
  /** Milestone metrics */
  milestones: {
    totalMilestones: number;
    completedMilestones: number;
    overdueMilestones: number;
    upcomingMilestones: number;
  };
  /** Resource allocation */
  resources: {
    timeSpent: number; // hours
    estimatedTimeRemaining: number; // hours
    resourceUtilization: number; // 0-100
    bottlenecks: string[];
  };
}

// ============================================================================
// DEADLINE MANAGEMENT TYPES
// ============================================================================

export interface DeadlineAlert {
  /** Alert ID */
  id: string;
  /** Task or goal ID */
  entityId: string;
  /** Entity type */
  entityType: 'task' | 'goal' | 'milestone';
  /** Alert type */
  type: 'approaching' | 'overdue' | 'critical';
  /** Alert severity */
  severity: 'low' | 'medium' | 'high' | 'critical';
  /** Alert message */
  message: string;
  /** Days until/past deadline */
  daysFromDeadline: number;
  /** Whether alert has been acknowledged */
  acknowledged: boolean;
  /** Alert created timestamp */
  createdAt: Date;
}

export interface DeadlineManagement {
  /** Upcoming deadlines */
  upcoming: {
    today: DeadlineAlert[];
    thisWeek: DeadlineAlert[];
    nextWeek: DeadlineAlert[];
    thisMonth: DeadlineAlert[];
  };
  /** Overdue items */
  overdue: {
    tasks: DeadlineAlert[];
    goals: DeadlineAlert[];
    milestones: DeadlineAlert[];
  };
  /** Critical alerts */
  critical: DeadlineAlert[];
  /** Alert preferences */
  preferences: {
    enableNotifications: boolean;
    advanceWarningDays: number[];
    notificationChannels: ('email' | 'push' | 'in_app')[];
    quietHours: {
      start: string; // HH:MM
      end: string; // HH:MM
    };
  };
}

// ============================================================================
// AUTOMATION TYPES
// ============================================================================

export interface TaskAutomationRule {
  /** Rule ID */
  id: string;
  /** User ID */
  userId: string;
  /** Rule name */
  name: string;
  /** Rule description */
  description?: string;
  /** Whether rule is active */
  isActive: boolean;
  /** Trigger conditions */
  trigger: {
    type: 'task_completed' | 'goal_progress' | 'deadline_approaching' | 'schedule';
    conditions: Record<string, any>;
  };
  /** Actions to perform */
  actions: {
    type: 'create_task' | 'update_task' | 'send_notification' | 'update_goal';
    parameters: Record<string, any>;
  }[];
  /** Rule execution history */
  executionHistory: {
    executedAt: Date;
    success: boolean;
    error?: string;
  }[];
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface AdvancedTaskApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: Date;
    processingTime: number;
  };
}

export interface TaskDependencyResponse extends AdvancedTaskApiResponse<TaskDependencyGraph> {}
export interface MilestoneResponse extends AdvancedTaskApiResponse<Milestone> {}
export interface PriorityMatrixResponse extends AdvancedTaskApiResponse<PriorityMatrix> {}
export interface TaskAnalyticsResponse extends AdvancedTaskApiResponse<TaskAnalytics> {}
export interface GoalAnalyticsResponse extends AdvancedTaskApiResponse<GoalAnalytics> {}
export interface RecurringTaskResponse extends AdvancedTaskApiResponse<RecurringTask> {}
export interface DeadlineManagementResponse extends AdvancedTaskApiResponse<DeadlineManagement> {}
