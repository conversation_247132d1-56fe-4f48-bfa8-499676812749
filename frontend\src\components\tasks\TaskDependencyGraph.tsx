import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  AccountTreeRounded,
  AddLinkRounded,
  RemoveLinkRounded,
  TimelineRounded,
  WarningRounded,
  InfoRounded,
  RefreshRounded,
  FullscreenRounded,
  ZoomInRounded,
  ZoomOutRounded,
} from '@mui/icons-material';

import type { Task } from '@/store/api/tasksApi';
import type { TaskDependency, TaskDependencyGraph } from '@/types/advanced-tasks';
import { useNotification } from '@/components/NotificationProvider';

/**
 * Task Dependency Graph Component
 * TODO: Implement actual graph visualization library (D3.js, vis.js, or similar)
 */

interface TaskDependencyGraphProps {
  tasks: Task[];
  onDependencyCreate?: (sourceId: string, targetId: string, type: string) => void;
  onDependencyDelete?: (dependencyId: string) => void;
  onTaskSelect?: (taskId: string) => void;
  className?: string;
}

interface GraphNode {
  id: string;
  label: string;
  status: string;
  priority: string;
  x: number;
  y: number;
  isBlocking: boolean;
  isBlocked: boolean;
  isCritical: boolean;
}

interface GraphEdge {
  id: string;
  from: string;
  to: string;
  type: string;
  isCritical: boolean;
}

export const TaskDependencyGraph: React.FC<TaskDependencyGraphProps> = ({
  tasks,
  onDependencyCreate,
  onDependencyDelete,
  onTaskSelect,
  className,
}) => {
  const { showSuccess, showError } = useNotification();
  
  // State
  const [dependencyGraph, setDependencyGraph] = useState<TaskDependencyGraph | null>(null);
  const [loading, setLoading] = useState(false);
  const [addDependencyOpen, setAddDependencyOpen] = useState(false);
  const [selectedSourceTask, setSelectedSourceTask] = useState<string>('');
  const [selectedTargetTask, setSelectedTargetTask] = useState<string>('');
  const [dependencyType, setDependencyType] = useState<string>('finish_to_start');
  const [graphNodes, setGraphNodes] = useState<GraphNode[]>([]);
  const [graphEdges, setGraphEdges] = useState<GraphEdge[]>([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  
  // Refs
  const graphContainerRef = useRef<HTMLDivElement>(null);

  // Load dependency graph on tasks change
  useEffect(() => {
    if (tasks.length > 0) {
      loadDependencyGraph();
    }
  }, [tasks]);

  // Load dependency graph
  const loadDependencyGraph = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      const mockGraph = await generateMockDependencyGraph(tasks);
      setDependencyGraph(mockGraph);
      
      // Convert to graph visualization format
      const nodes = convertTasksToNodes(mockGraph.tasks, mockGraph);
      const edges = convertDependenciesToEdges(mockGraph.dependencies, mockGraph);
      
      setGraphNodes(nodes);
      setGraphEdges(edges);
      
    } catch (error) {
      console.error('Error loading dependency graph:', error);
      showError('Failed to load dependency graph');
    } finally {
      setLoading(false);
    }
  };

  // Handle add dependency
  const handleAddDependency = async () => {
    try {
      if (!selectedSourceTask || !selectedTargetTask) {
        showError('Please select both source and target tasks');
        return;
      }

      if (selectedSourceTask === selectedTargetTask) {
        showError('A task cannot depend on itself');
        return;
      }

      // TODO: Validate for circular dependencies
      
      await onDependencyCreate?.(selectedSourceTask, selectedTargetTask, dependencyType);
      
      setAddDependencyOpen(false);
      setSelectedSourceTask('');
      setSelectedTargetTask('');
      setDependencyType('finish_to_start');
      
      // Reload graph
      await loadDependencyGraph();
      
      showSuccess('Dependency added successfully');
      
    } catch (error) {
      showError('Failed to add dependency');
    }
  };

  // Handle zoom
  const handleZoom = (direction: 'in' | 'out') => {
    const newZoom = direction === 'in' 
      ? Math.min(zoomLevel * 1.2, 3) 
      : Math.max(zoomLevel / 1.2, 0.3);
    setZoomLevel(newZoom);
  };

  // Convert tasks to graph nodes
  const convertTasksToNodes = (tasks: Task[], graph: TaskDependencyGraph): GraphNode[] => {
    return tasks.map((task, index) => ({
      id: task.id,
      label: task.title,
      status: task.status,
      priority: task.priority,
      x: (index % 5) * 200 + 100, // Simple grid layout
      y: Math.floor(index / 5) * 150 + 100,
      isBlocking: graph.blockingTasks.includes(task.id),
      isBlocked: graph.blockedTasks.includes(task.id),
      isCritical: graph.criticalPath.includes(task.id),
    }));
  };

  // Convert dependencies to graph edges
  const convertDependenciesToEdges = (dependencies: TaskDependency[], graph: TaskDependencyGraph): GraphEdge[] => {
    return dependencies.map((dep) => ({
      id: dep.id,
      from: dep.sourceTaskId,
      to: dep.targetTaskId,
      type: dep.type,
      isCritical: graph.criticalPath.includes(dep.sourceTaskId) && graph.criticalPath.includes(dep.targetTaskId),
    }));
  };

  // Get node color based on status and properties
  const getNodeColor = (node: GraphNode) => {
    if (node.isCritical) return '#f44336'; // Red for critical path
    if (node.isBlocking) return '#ff9800'; // Orange for blocking
    if (node.isBlocked) return '#9c27b0'; // Purple for blocked
    
    switch (node.status) {
      case 'completed': return '#4caf50'; // Green
      case 'in_progress': return '#2196f3'; // Blue
      case 'todo': return '#757575'; // Grey
      default: return '#757575';
    }
  };

  // Get edge color
  const getEdgeColor = (edge: GraphEdge) => {
    return edge.isCritical ? '#f44336' : '#757575';
  };

  // Render graph node
  const renderGraphNode = (node: GraphNode) => (
    <Box
      key={node.id}
      sx={{
        position: 'absolute',
        left: node.x * zoomLevel,
        top: node.y * zoomLevel,
        transform: 'translate(-50%, -50%)',
        cursor: 'pointer',
      }}
      onClick={() => onTaskSelect?.(node.id)}
    >
      <Card
        sx={{
          minWidth: 120 * zoomLevel,
          border: 2,
          borderColor: getNodeColor(node),
          bgcolor: 'background.paper',
          '&:hover': {
            boxShadow: 4,
          },
        }}
      >
        <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
          <Typography 
            variant="caption" 
            fontWeight={600}
            sx={{ 
              fontSize: Math.max(10 * zoomLevel, 8),
              lineHeight: 1.2,
              display: 'block',
            }}
          >
            {node.label.length > 20 ? `${node.label.substring(0, 20)}...` : node.label}
          </Typography>
          
          <Box display="flex" gap={0.5} mt={0.5} flexWrap="wrap">
            {node.isCritical && (
              <Chip 
                label="Critical" 
                size="small" 
                color="error" 
                sx={{ height: 16 * zoomLevel, fontSize: 8 * zoomLevel }}
              />
            )}
            {node.isBlocking && (
              <Chip 
                label="Blocking" 
                size="small" 
                color="warning" 
                sx={{ height: 16 * zoomLevel, fontSize: 8 * zoomLevel }}
              />
            )}
            {node.isBlocked && (
              <Chip 
                label="Blocked" 
                size="small" 
                color="secondary" 
                sx={{ height: 16 * zoomLevel, fontSize: 8 * zoomLevel }}
              />
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );

  // Render graph edge (simplified line representation)
  const renderGraphEdge = (edge: GraphEdge) => {
    const fromNode = graphNodes.find(n => n.id === edge.from);
    const toNode = graphNodes.find(n => n.id === edge.to);
    
    if (!fromNode || !toNode) return null;

    const x1 = fromNode.x * zoomLevel;
    const y1 = fromNode.y * zoomLevel;
    const x2 = toNode.x * zoomLevel;
    const y2 = toNode.y * zoomLevel;

    return (
      <line
        key={edge.id}
        x1={x1}
        y1={y1}
        x2={x2}
        y2={y2}
        stroke={getEdgeColor(edge)}
        strokeWidth={edge.isCritical ? 3 : 1}
        markerEnd="url(#arrowhead)"
      />
    );
  };

  return (
    <Box className={className}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Task Dependencies
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Visualize task relationships and critical path
          </Typography>
        </Box>
        
        <Box display="flex" gap={1}>
          <Tooltip title="Add dependency">
            <IconButton onClick={() => setAddDependencyOpen(true)}>
              <AddLinkRounded />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh graph">
            <IconButton onClick={loadDependencyGraph} disabled={loading}>
              <RefreshRounded />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom in">
            <IconButton onClick={() => handleZoom('in')}>
              <ZoomInRounded />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom out">
            <IconButton onClick={() => handleZoom('out')}>
              <ZoomOutRounded />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Warnings */}
      {dependencyGraph?.circularDependencies.length > 0 && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2" fontWeight={600}>
            Circular Dependencies Detected
          </Typography>
          <Typography variant="body2">
            {dependencyGraph.circularDependencies.length} circular dependency chains found. 
            This may cause scheduling conflicts.
          </Typography>
        </Alert>
      )}

      {/* Graph Container */}
      <Card sx={{ height: 600, overflow: 'hidden', position: 'relative' }}>
        <CardContent sx={{ height: '100%', p: 0 }}>
          {loading ? (
            <Box display="flex" alignItems="center" justifyContent="center" height="100%">
              <Typography>Loading dependency graph...</Typography>
            </Box>
          ) : graphNodes.length === 0 ? (
            <Box display="flex" alignItems="center" justifyContent="center" height="100%">
              <Box textAlign="center">
                <AccountTreeRounded sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Dependencies
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Add task dependencies to visualize relationships
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddLinkRounded />}
                  onClick={() => setAddDependencyOpen(true)}
                >
                  Add Dependency
                </Button>
              </Box>
            </Box>
          ) : (
            <Box
              ref={graphContainerRef}
              sx={{
                width: '100%',
                height: '100%',
                overflow: 'auto',
                position: 'relative',
                bgcolor: 'grey.50',
              }}
            >
              {/* SVG for edges */}
              <svg
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'none',
                  zIndex: 1,
                }}
              >
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="#757575"
                    />
                  </marker>
                </defs>
                {graphEdges.map(renderGraphEdge)}
              </svg>

              {/* Nodes */}
              <Box sx={{ position: 'relative', zIndex: 2 }}>
                {graphNodes.map(renderGraphNode)}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Legend */}
      {dependencyGraph && (
        <Box mt={2}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Legend
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={2}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Box width={16} height={16} bgcolor="#f44336" borderRadius={1} />
                  <Typography variant="body2">Critical Path</Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1}>
                  <Box width={16} height={16} bgcolor="#ff9800" borderRadius={1} />
                  <Typography variant="body2">Blocking Tasks</Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1}>
                  <Box width={16} height={16} bgcolor="#9c27b0" borderRadius={1} />
                  <Typography variant="body2">Blocked Tasks</Typography>
                </Box>
                <Box display="flex" alignItems="center" gap={1}>
                  <Box width={16} height={16} bgcolor="#4caf50" borderRadius={1} />
                  <Typography variant="body2">Completed</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Add Dependency Dialog */}
      <Dialog open={addDependencyOpen} onClose={() => setAddDependencyOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Task Dependency</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={3} pt={1}>
            <FormControl fullWidth>
              <InputLabel>Source Task (depends on target)</InputLabel>
              <Select
                value={selectedSourceTask}
                onChange={(e) => setSelectedSourceTask(e.target.value)}
                label="Source Task (depends on target)"
              >
                {tasks.map((task) => (
                  <MenuItem key={task.id} value={task.id}>
                    {task.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Target Task (dependency)</InputLabel>
              <Select
                value={selectedTargetTask}
                onChange={(e) => setSelectedTargetTask(e.target.value)}
                label="Target Task (dependency)"
              >
                {tasks.filter(task => task.id !== selectedSourceTask).map((task) => (
                  <MenuItem key={task.id} value={task.id}>
                    {task.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Dependency Type</InputLabel>
              <Select
                value={dependencyType}
                onChange={(e) => setDependencyType(e.target.value)}
                label="Dependency Type"
              >
                <MenuItem value="finish_to_start">Finish to Start</MenuItem>
                <MenuItem value="start_to_start">Start to Start</MenuItem>
                <MenuItem value="finish_to_finish">Finish to Finish</MenuItem>
                <MenuItem value="start_to_finish">Start to Finish</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDependencyOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleAddDependency}>
            Add Dependency
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Mock dependency graph generation
async function generateMockDependencyGraph(tasks: Task[]): Promise<TaskDependencyGraph> {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Generate some mock dependencies
      const dependencies: TaskDependency[] = [];
      const criticalPath: string[] = [];
      const blockingTasks: string[] = [];
      const blockedTasks: string[] = [];

      // Create a few random dependencies
      for (let i = 0; i < Math.min(tasks.length - 1, 3); i++) {
        const sourceTask = tasks[i];
        const targetTask = tasks[i + 1];
        
        dependencies.push({
          id: `dep-${i}`,
          sourceTaskId: sourceTask.id,
          targetTaskId: targetTask.id,
          type: 'finish_to_start',
          lagHours: 0,
          isHard: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        blockingTasks.push(targetTask.id);
        blockedTasks.push(sourceTask.id);
      }

      // Mock critical path
      if (tasks.length >= 2) {
        criticalPath.push(tasks[0].id, tasks[1].id);
      }

      resolve({
        tasks,
        dependencies,
        criticalPath,
        blockingTasks,
        blockedTasks,
        circularDependencies: [], // No circular dependencies in mock
      });
    }, 1000);
  });
}
