// MindSync Neo4j Database Initialization
// This script sets up the initial graph database structure and constraints

// Create constraints for unique properties
CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE;
CREATE CONSTRAINT task_id_unique IF NOT EXISTS FOR (t:Task) REQUIRE t.id IS UNIQUE;
CREATE CONSTRAINT goal_id_unique IF NOT EXISTS FOR (g:Goal) REQUIRE g.id IS UNIQUE;
CREATE CONSTRAINT concept_id_unique IF NOT EXISTS FOR (c:Concept) REQUIRE c.id IS UNIQUE;
CREATE CONSTRAINT project_id_unique IF NOT EXISTS FOR (p:Project) REQUIRE p.id IS UNIQUE;
CREATE CONSTRAINT note_id_unique IF NOT EXISTS FOR (n:Note) REQUIRE n.id IS UNIQUE;

// Create indexes for better performance
CREATE INDEX user_email_index IF NOT EXISTS FOR (u:User) ON (u.email);
CREATE INDEX task_title_index IF NOT EXISTS FOR (t:Task) ON (t.title);
CREATE INDEX goal_title_index IF NOT EXISTS FOR (g:Goal) ON (g.title);
CREATE INDEX concept_name_index IF NOT EXISTS FOR (c:Concept) ON (c.name);
CREATE INDEX project_name_index IF NOT EXISTS FOR (p:Project) ON (p.name);
CREATE INDEX note_title_index IF NOT EXISTS FOR (n:Note) ON (n.title);

// Create full-text search indexes
CALL db.index.fulltext.createNodeIndex("taskSearch", ["Task"], ["title", "description"]);
CALL db.index.fulltext.createNodeIndex("goalSearch", ["Goal"], ["title", "description"]);
CALL db.index.fulltext.createNodeIndex("conceptSearch", ["Concept"], ["name", "description"]);
CALL db.index.fulltext.createNodeIndex("noteSearch", ["Note"], ["title", "content"]);

// Sample node creation (will be replaced by actual data)
// These are just examples to show the graph structure

// Create sample user
CREATE (u:User {
    id: "sample-user-id",
    email: "<EMAIL>",
    name: "Demo User",
    created_at: datetime()
});

// Create sample goals hierarchy
CREATE (vision:Goal {
    id: "vision-1",
    title: "Become a Productivity Expert",
    description: "Master productivity systems and help others achieve their goals",
    type: "vision",
    status: "active",
    created_at: datetime()
});

CREATE (objective1:Goal {
    id: "objective-1",
    title: "Learn Advanced Productivity Techniques",
    description: "Study and implement various productivity methodologies",
    type: "objective",
    status: "active",
    created_at: datetime()
});

CREATE (kr1:Goal {
    id: "kr-1",
    title: "Complete 5 Productivity Courses",
    description: "Finish online courses on GTD, PARA, Zettelkasten, etc.",
    type: "key_result",
    status: "active",
    target_value: 5,
    current_value: 2,
    created_at: datetime()
});

// Create sample tasks
CREATE (task1:Task {
    id: "task-1",
    title: "Read Getting Things Done book",
    description: "Complete David Allen's GTD methodology book",
    status: "in_progress",
    priority: "high",
    complexity_score: 6,
    estimated_duration: 480,
    created_at: datetime()
});

CREATE (task2:Task {
    id: "task-2",
    title: "Set up digital note-taking system",
    description: "Implement a digital second brain using Obsidian or similar",
    status: "todo",
    priority: "medium",
    complexity_score: 4,
    estimated_duration: 120,
    created_at: datetime()
});

// Create sample concepts
CREATE (gtd:Concept {
    id: "concept-gtd",
    name: "Getting Things Done",
    description: "Productivity methodology by David Allen",
    type: "methodology",
    created_at: datetime()
});

CREATE (secondBrain:Concept {
    id: "concept-second-brain",
    name: "Second Brain",
    description: "Personal knowledge management system",
    type: "concept",
    created_at: datetime()
});

// Create sample projects
CREATE (project1:Project {
    id: "project-1",
    name: "Personal Productivity System",
    description: "Build and implement a comprehensive personal productivity system",
    status: "active",
    created_at: datetime()
});

// Create sample notes
CREATE (note1:Note {
    id: "note-1",
    title: "GTD Key Principles",
    content: "1. Capture everything 2. Clarify what it means 3. Organize by context 4. Review regularly 5. Engage with confidence",
    type: "summary",
    created_at: datetime()
});

// Create relationships
// Goal hierarchy
CREATE (vision)-[:HAS_OBJECTIVE]->(objective1);
CREATE (objective1)-[:HAS_KEY_RESULT]->(kr1);

// Task-Goal relationships
CREATE (task1)-[:CONTRIBUTES_TO]->(kr1);
CREATE (task2)-[:CONTRIBUTES_TO]->(kr1);

// Task-Project relationships
CREATE (task1)-[:BELONGS_TO]->(project1);
CREATE (task2)-[:BELONGS_TO]->(project1);

// Concept relationships
CREATE (task1)-[:RELATES_TO]->(gtd);
CREATE (task2)-[:RELATES_TO]->(secondBrain);
CREATE (note1)-[:DESCRIBES]->(gtd);

// Knowledge connections
CREATE (gtd)-[:SUPPORTS]->(secondBrain);
CREATE (secondBrain)-[:ENABLES]->(project1);

// User ownership relationships
CREATE (u)-[:OWNS]->(vision);
CREATE (u)-[:OWNS]->(objective1);
CREATE (u)-[:OWNS]->(kr1);
CREATE (u)-[:OWNS]->(task1);
CREATE (u)-[:OWNS]->(task2);
CREATE (u)-[:OWNS]->(project1);
CREATE (u)-[:OWNS]->(note1);
CREATE (u)-[:KNOWS]->(gtd);
CREATE (u)-[:KNOWS]->(secondBrain);

// Temporal relationships (for tracking progress over time)
CREATE (task1)-[:PRECEDED_BY]->(task2);

// Similarity relationships (will be populated by ML algorithms)
// These would be created dynamically based on content similarity
CREATE (task1)-[:SIMILAR_TO {similarity_score: 0.75}]->(task2);

// Create some sample patterns for template generation
CREATE (pattern1:Pattern {
    id: "pattern-1",
    name: "Learning Project Pattern",
    description: "Common structure for learning-based projects",
    success_rate: 0.85,
    usage_count: 12,
    created_at: datetime()
});

CREATE (pattern1)-[:INCLUDES]->(task1);
CREATE (pattern1)-[:INCLUDES]->(task2);
CREATE (pattern1)-[:APPLIES_TO]->(project1);

// Create mood and context nodes for emotion-aware scheduling
CREATE (context1:Context {
    id: "context-1",
    name: "Deep Work",
    description: "Focused, uninterrupted work time",
    optimal_duration: 90,
    energy_requirement: "high"
});

CREATE (context2:Context {
    id: "context-2",
    name: "Administrative",
    description: "Email, scheduling, and organizational tasks",
    optimal_duration: 30,
    energy_requirement: "low"
});

// Link tasks to contexts
CREATE (task1)-[:REQUIRES]->(context1);
CREATE (task2)-[:REQUIRES]->(context2);

// Create time-based relationships for scheduling
CREATE (morning:TimeSlot {
    id: "morning-slot",
    name: "Morning Focus",
    start_time: "09:00",
    end_time: "11:00",
    energy_level: "high",
    optimal_for: ["deep_work", "creative_tasks"]
});

CREATE (afternoon:TimeSlot {
    id: "afternoon-slot",
    name: "Afternoon Admin",
    start_time: "14:00",
    end_time: "16:00",
    energy_level: "medium",
    optimal_for: ["administrative", "communication"]
});

// Link contexts to optimal time slots
CREATE (context1)-[:OPTIMAL_IN]->(morning);
CREATE (context2)-[:OPTIMAL_IN]->(afternoon);

// This initialization script provides the foundation for the knowledge graph
// Additional nodes and relationships will be created dynamically as users interact with the system
