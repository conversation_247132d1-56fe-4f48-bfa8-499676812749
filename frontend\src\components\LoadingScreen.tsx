import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Fade,
  LinearProgress,
} from '@mui/material';
import { PsychologyRounded } from '@mui/icons-material';

interface LoadingScreenProps {
  message?: string;
  progress?: number;
  showProgress?: boolean;
  size?: 'small' | 'medium' | 'large';
  fullScreen?: boolean;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  progress,
  showProgress = false,
  size = 'medium',
  fullScreen = true,
}) => {
  const sizeMap = {
    small: 32,
    medium: 48,
    large: 64,
  };

  const containerSx = fullScreen
    ? {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'background.default',
        zIndex: 9999,
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 200,
        width: '100%',
      };

  return (
    <Fade in timeout={300}>
      <Box sx={containerSx}>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap={3}
        >
          {/* Logo and spinner */}
          <Box position="relative" display="flex" alignItems="center" justifyContent="center">
            <PsychologyRounded
              sx={{
                fontSize: sizeMap[size],
                color: 'primary.main',
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': {
                    opacity: 1,
                  },
                  '50%': {
                    opacity: 0.5,
                  },
                  '100%': {
                    opacity: 1,
                  },
                },
              }}
            />
            <CircularProgress
              size={sizeMap[size] + 16}
              thickness={2}
              sx={{
                position: 'absolute',
                color: 'primary.main',
                opacity: 0.3,
              }}
            />
          </Box>

          {/* Loading message */}
          <Typography
            variant={size === 'large' ? 'h6' : 'body1'}
            color="text.secondary"
            textAlign="center"
          >
            {message}
          </Typography>

          {/* Progress bar */}
          {showProgress && (
            <Box width={200}>
              <LinearProgress
                variant={progress !== undefined ? 'determinate' : 'indeterminate'}
                value={progress}
                sx={{
                  height: 4,
                  borderRadius: 2,
                }}
              />
              {progress !== undefined && (
                <Typography
                  variant="caption"
                  color="text.secondary"
                  textAlign="center"
                  display="block"
                  mt={1}
                >
                  {Math.round(progress)}%
                </Typography>
              )}
            </Box>
          )}
        </Box>
      </Box>
    </Fade>
  );
};

// Inline loading component for smaller areas
export const InlineLoading: React.FC<{
  message?: string;
  size?: number;
}> = ({ message = 'Loading...', size = 24 }) => (
  <Box
    display="flex"
    alignItems="center"
    justifyContent="center"
    gap={2}
    py={2}
  >
    <CircularProgress size={size} />
    <Typography variant="body2" color="text.secondary">
      {message}
    </Typography>
  </Box>
);

// Button loading state
export const ButtonLoading: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  size?: number;
}> = ({ loading, children, size = 20 }) => (
  <Box display="flex" alignItems="center" gap={1}>
    {loading && <CircularProgress size={size} />}
    {children}
  </Box>
);
