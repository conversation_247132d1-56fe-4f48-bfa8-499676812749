import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Box,
  Chip,
  Avatar,
  Divider,
} from '@mui/material';
import {
  TextFieldsRounded,
  MicRounded,
  PhotoCameraRounded,
  EmailRounded,
  LinkRounded,
  HistoryRounded,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

import type { ProcessedCapture } from '@/store/api/captureApi';
import { InlineLoading } from '@/components/LoadingScreen';

interface RecentCapturesWidgetProps {
  captures: ProcessedCapture[];
  loading: boolean;
}

const getCaptureIcon = (type: string) => {
  switch (type) {
    case 'text':
      return <TextFieldsRounded />;
    case 'voice':
      return <MicRounded />;
    case 'image':
      return <PhotoCameraRounded />;
    case 'email':
      return <EmailRounded />;
    case 'web_clip':
      return <LinkRounded />;
    default:
      return <TextFieldsRounded />;
  }
};

const getCaptureColor = (type: string): 'primary' | 'secondary' | 'success' | 'warning' | 'info' => {
  switch (type) {
    case 'text':
      return 'primary';
    case 'voice':
      return 'secondary';
    case 'image':
      return 'success';
    case 'email':
      return 'warning';
    case 'web_clip':
      return 'info';
    default:
      return 'primary';
  }
};

const truncateText = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const RecentCapturesWidget: React.FC<RecentCapturesWidgetProps> = ({
  captures,
  loading,
}) => {
  const navigate = useNavigate();

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight={600} gutterBottom>
            Recent Captures
          </Typography>
          <InlineLoading message="Loading captures..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Recent Captures
          </Typography>
          <Button
            size="small"
            endIcon={<HistoryRounded />}
            onClick={() => navigate('/capture/history')}
          >
            View All
          </Button>
        </Box>

        {captures.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={4}
            textAlign="center"
          >
            <Avatar
              sx={{
                width: 64,
                height: 64,
                bgcolor: 'grey.100',
                color: 'grey.400',
                mb: 2,
              }}
            >
              <HistoryRounded fontSize="large" />
            </Avatar>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              No captures yet
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Start capturing your thoughts and ideas!
            </Typography>
          </Box>
        ) : (
          <List disablePadding>
            {captures.map((capture, index) => (
              <React.Fragment key={capture.id}>
                <ListItem
                  disablePadding
                  sx={{
                    py: 1,
                    cursor: 'pointer',
                    borderRadius: 1,
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  }}
                  onClick={() => navigate(`/capture/history?id=${capture.id}`)}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: `${getCaptureColor(capture.type)}.main`,
                      }}
                    >
                      {getCaptureIcon(capture.type)}
                    </Avatar>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                        <Typography variant="body2" fontWeight={500}>
                          {truncateText(capture.processedContent || capture.originalContent)}
                        </Typography>
                        <Chip
                          label={capture.type}
                          size="small"
                          color={getCaptureColor(capture.type)}
                          variant="outlined"
                          sx={{ height: 20, fontSize: '0.625rem' }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(capture.createdAt), { addSuffix: true })}
                        </Typography>
                        {capture.extractedEntities.length > 0 && (
                          <Typography variant="caption" color="primary.main">
                            {capture.extractedEntities.length} entities found
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                
                {index < captures.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        )}

        {captures.length > 0 && (
          <Box mt={2}>
            <Button
              fullWidth
              variant="outlined"
              size="small"
              onClick={() => navigate('/capture')}
            >
              Create New Capture
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
