# Environment Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
DATABASE_URL=postgresql://mindsync_user:mindsync_password@localhost:5432/mindsync

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=mindsync_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# API Keys
HUGGINGFACE_API_KEY=your-huggingface-api-key
OPENAI_API_KEY=your-openai-api-key-optional

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# External API Configuration
SPEECH_TO_TEXT_API_URL=https://api.example.com/speech-to-text
OCR_API_URL=https://api.example.com/ocr

# Calendar Integration
GOOGLE_CALENDAR_API_KEY=your-google-calendar-api-key
OUTLOOK_CLIENT_ID=your-outlook-client-id
OUTLOOK_CLIENT_SECRET=your-outlook-client-secret

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret

# Development/Testing
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_MORGAN_LOGGING=true
