version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    container_name: mindsync-postgres
    environment:
      POSTGRES_DB: mindsync
      POSTGRES_USER: mindsync_user
      POSTGRES_PASSWORD: mindsync_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/postgresql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - mindsync-network
    restart: unless-stopped

  # Neo4j Graph Database
  neo4j:
    image: neo4j:4.4-community
    container_name: mindsync-neo4j
    environment:
      NEO4J_AUTH: neo4j/mindsync_password
      NEO4J_dbms_memory_heap_initial__size: 512m
      NEO4J_dbms_memory_heap_max__size: 1G
      NEO4J_dbms_memory_pagecache_size: 512m
      NEO4J_dbms_security_procedures_unrestricted: apoc.*
      NEO4J_dbms_security_procedures_allowlist: apoc.*
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
      - ./database/neo4j/init.cypher:/var/lib/neo4j/import/init.cypher
    networks:
      - mindsync-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:6-alpine
    container_name: mindsync-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mindsync-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: mindsync-backend
    environment:
      NODE_ENV: development
      PORT: 5000
      DATABASE_URL: **********************************************************/mindsync
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: mindsync_password
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-development-jwt-secret-change-in-production
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - neo4j
      - redis
    networks:
      - mindsync-network
    restart: unless-stopped
    command: npm run dev

  # Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: mindsync-frontend
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_WS_URL: ws://localhost:5000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - mindsync-network
    restart: unless-stopped
    command: npm start

  # Elasticsearch (Optional - for advanced search)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    container_name: mindsync-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - mindsync-network
    restart: unless-stopped
    profiles:
      - search

volumes:
  postgres_data:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  redis_data:
  elasticsearch_data:

networks:
  mindsync-network:
    driver: bridge
