import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { authService, validatePassword, validateEmail, AuthenticatedRequest } from '@/middleware/auth';
import { authRateLimiter, passwordResetRateLimiter } from '@/middleware/rateLimiter';
import { asyncHandler, ValidationError, AuthenticationError, ConflictError } from '@/middleware/errorHandler';
import { logger, logSecurityEvent } from '@/utils/logger';

const router = Router();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.post('/register', 
  authRateLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long'),
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('First name must be between 1 and 100 characters'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Last name must be between 1 and 100 characters')
  ],
  asyncHandler(async (req: Request, res: Response) => {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { email, password, firstName, lastName } = req.body;

    // Validate email format
    if (!validateEmail(email)) {
      throw new ValidationError('Invalid email format');
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      throw new ValidationError(passwordValidation.errors.join(', '));
    }

    // Check if user already exists
    const existingUser = await authService.getUserByEmail(email);
    if (existingUser) {
      logSecurityEvent('Registration attempt with existing email', undefined, req.ip, { email });
      throw new ConflictError('Email already registered');
    }

    // Create user
    const user = await authService.createUser({
      email,
      password,
      firstName,
      lastName
    });

    // Generate tokens
    const tokens = authService.generateTokenPair({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    // Store refresh token
    await authService.storeRefreshToken(user.id, tokens.refreshToken);

    logger.info(`New user registered: ${user.email}`);

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        isVerified: user.is_verified
      },
      tokens
    });
  })
);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login',
  authRateLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ],
  asyncHandler(async (req: Request, res: Response) => {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { email, password } = req.body;

    // Get user by email
    const user = await authService.getUserByEmail(email);
    if (!user) {
      logSecurityEvent('Login attempt with non-existent email', undefined, req.ip, { email });
      throw new AuthenticationError('Invalid email or password');
    }

    // Verify password
    const isPasswordValid = await authService.verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      logSecurityEvent('Login attempt with invalid password', user.id, req.ip, { email });
      throw new AuthenticationError('Invalid email or password');
    }

    // Generate tokens
    const tokens = authService.generateTokenPair({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    // Store refresh token
    await authService.storeRefreshToken(user.id, tokens.refreshToken);

    // Update last login
    await authService.updateLastLogin(user.id);

    logger.info(`User logged in: ${user.email}`);

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        isVerified: user.is_verified,
        avatarUrl: user.avatar_url,
        timezone: user.timezone
      },
      tokens
    });
  })
);

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh',
  [
    body('refreshToken')
      .notEmpty()
      .withMessage('Refresh token is required')
  ],
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { refreshToken } = req.body;

    // Verify refresh token
    const decoded = authService.verifyRefreshToken(refreshToken);

    // Get user
    const user = await authService.getUserById(decoded.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.is_verified) {
      throw new AuthenticationError('Email not verified');
    }

    // Generate new token pair
    const tokens = authService.generateTokenPair({
      userId: user.id,
      email: user.email,
      role: user.role
    });

    // Store new refresh token and revoke old one
    await authService.revokeRefreshToken(user.id, refreshToken);
    await authService.storeRefreshToken(user.id, tokens.refreshToken);

    res.json({
      message: 'Token refreshed successfully',
      tokens
    });
  })
);

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout',
  [
    body('refreshToken')
      .notEmpty()
      .withMessage('Refresh token is required')
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { refreshToken } = req.body;

    // Get user ID from token if available
    let userId: string | undefined;
    
    try {
      const authHeader = req.headers.authorization;
      if (authHeader) {
        const token = authHeader.split(' ')[1];
        if (token) {
          const decoded = authService.verifyAccessToken(token);
          userId = decoded.userId;
        }
      }
    } catch (error) {
      // Token might be expired, try to get user ID from refresh token
      try {
        const decoded = authService.verifyRefreshToken(refreshToken);
        userId = decoded.userId;
      } catch (refreshError) {
        // Both tokens invalid, but we can still "logout"
      }
    }

    // Revoke refresh token if we have a user ID
    if (userId) {
      await authService.revokeRefreshToken(userId, refreshToken);
      logger.info(`User logged out: ${userId}`);
    }

    res.json({
      message: 'Logout successful'
    });
  })
);

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Authentication required
 */
router.get('/me',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      throw new AuthenticationError('Authorization header missing');
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      throw new AuthenticationError('Access token missing');
    }

    // Verify token
    const decoded = authService.verifyAccessToken(token);

    // Get user details
    const user = await authService.getUserById(decoded.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    res.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        isVerified: user.is_verified,
        avatarUrl: user.avatar_url,
        timezone: user.timezone,
        preferences: user.preferences,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      }
    });
  })
);

/**
 * @swagger
 * /api/auth/verify-token:
 *   post:
 *     summary: Verify if token is valid
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token is valid
 *       401:
 *         description: Token is invalid
 */
router.post('/verify-token',
  [
    body('token')
      .notEmpty()
      .withMessage('Token is required')
  ],
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { token } = req.body;

    try {
      const decoded = authService.verifyAccessToken(token);
      const user = await authService.getUserById(decoded.userId);

      if (!user || !user.is_verified) {
        throw new AuthenticationError('Invalid token');
      }

      res.json({
        valid: true,
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        }
      });
    } catch (error) {
      throw new AuthenticationError('Invalid token');
    }
  })
);

export default router;
