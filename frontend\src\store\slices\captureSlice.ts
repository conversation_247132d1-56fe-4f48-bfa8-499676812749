import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { ProcessedCapture } from '../api/captureApi';

export interface CaptureState {
  currentCapture: ProcessedCapture | null;
  recentCaptures: ProcessedCapture[];
  captureHistory: ProcessedCapture[];
  searchResults: ProcessedCapture[];
  searchQuery: string;
  isProcessing: boolean;
  processingProgress: number;
  processingStatus: string;
  selectedCaptureType: 'text' | 'voice' | 'image' | 'email' | 'web_clip';
  filters: {
    type?: 'text' | 'voice' | 'image' | 'email' | 'web_clip';
    dateRange?: {
      start: string;
      end: string;
    };
    tags?: string[];
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: CaptureState = {
  currentCapture: null,
  recentCaptures: [],
  captureHistory: [],
  searchResults: [],
  searchQuery: '',
  isProcessing: false,
  processingProgress: 0,
  processingStatus: '',
  selectedCaptureType: 'text',
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
  },
};

const captureSlice = createSlice({
  name: 'capture',
  initialState,
  reducers: {
    setCurrentCapture: (state, action: PayloadAction<ProcessedCapture | null>) => {
      state.currentCapture = action.payload;
    },
    
    addRecentCapture: (state, action: PayloadAction<ProcessedCapture>) => {
      state.recentCaptures.unshift(action.payload);
      // Keep only the 10 most recent captures
      if (state.recentCaptures.length > 10) {
        state.recentCaptures = state.recentCaptures.slice(0, 10);
      }
    },
    
    setCaptureHistory: (state, action: PayloadAction<ProcessedCapture[]>) => {
      state.captureHistory = action.payload;
    },
    
    addToCaptureHistory: (state, action: PayloadAction<ProcessedCapture[]>) => {
      state.captureHistory.push(...action.payload);
    },
    
    setSearchResults: (state, action: PayloadAction<ProcessedCapture[]>) => {
      state.searchResults = action.payload;
    },
    
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    clearSearchResults: (state) => {
      state.searchResults = [];
      state.searchQuery = '';
    },
    
    setProcessing: (state, action: PayloadAction<boolean>) => {
      state.isProcessing = action.payload;
      if (!action.payload) {
        state.processingProgress = 0;
        state.processingStatus = '';
      }
    },
    
    setProcessingProgress: (state, action: PayloadAction<number>) => {
      state.processingProgress = action.payload;
    },
    
    setProcessingStatus: (state, action: PayloadAction<string>) => {
      state.processingStatus = action.payload;
    },
    
    setSelectedCaptureType: (state, action: PayloadAction<'text' | 'voice' | 'image' | 'email' | 'web_clip'>) => {
      state.selectedCaptureType = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<CaptureState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    setPagination: (state, action: PayloadAction<Partial<CaptureState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    
    resetPagination: (state) => {
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
      };
    },
    
    updateCaptureInHistory: (state, action: PayloadAction<ProcessedCapture>) => {
      const index = state.captureHistory.findIndex(capture => capture.id === action.payload.id);
      if (index !== -1) {
        state.captureHistory[index] = action.payload;
      }
      
      const recentIndex = state.recentCaptures.findIndex(capture => capture.id === action.payload.id);
      if (recentIndex !== -1) {
        state.recentCaptures[recentIndex] = action.payload;
      }
    },
    
    removeCaptureFromHistory: (state, action: PayloadAction<string>) => {
      state.captureHistory = state.captureHistory.filter(capture => capture.id !== action.payload);
      state.recentCaptures = state.recentCaptures.filter(capture => capture.id !== action.payload);
      state.searchResults = state.searchResults.filter(capture => capture.id !== action.payload);
    },
    
    clearCaptureHistory: (state) => {
      state.captureHistory = [];
      state.recentCaptures = [];
      state.searchResults = [];
      state.currentCapture = null;
    },
  },
});

export const {
  setCurrentCapture,
  addRecentCapture,
  setCaptureHistory,
  addToCaptureHistory,
  setSearchResults,
  setSearchQuery,
  clearSearchResults,
  setProcessing,
  setProcessingProgress,
  setProcessingStatus,
  setSelectedCaptureType,
  setFilters,
  clearFilters,
  setPagination,
  resetPagination,
  updateCaptureInHistory,
  removeCaptureFromHistory,
  clearCaptureHistory,
} = captureSlice.actions;

export default captureSlice.reducer;

// Selectors
export const selectCurrentCapture = (state: { capture: CaptureState }) => state.capture.currentCapture;
export const selectRecentCaptures = (state: { capture: CaptureState }) => state.capture.recentCaptures;
export const selectCaptureHistory = (state: { capture: CaptureState }) => state.capture.captureHistory;
export const selectSearchResults = (state: { capture: CaptureState }) => state.capture.searchResults;
export const selectSearchQuery = (state: { capture: CaptureState }) => state.capture.searchQuery;
export const selectIsProcessing = (state: { capture: CaptureState }) => state.capture.isProcessing;
export const selectProcessingProgress = (state: { capture: CaptureState }) => state.capture.processingProgress;
export const selectProcessingStatus = (state: { capture: CaptureState }) => state.capture.processingStatus;
export const selectSelectedCaptureType = (state: { capture: CaptureState }) => state.capture.selectedCaptureType;
export const selectCaptureFilters = (state: { capture: CaptureState }) => state.capture.filters;
export const selectCapturePagination = (state: { capture: CaptureState }) => state.capture.pagination;
