import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Goal } from '../api/goalsApi';

export interface GoalsState {
  goals: Goal[];
  currentGoal: Goal | null;
  selectedGoals: string[];
  goalHierarchy: Goal[];
  filters: {
    status?: 'active' | 'completed' | 'paused' | 'cancelled';
    parentId?: string;
    search?: string;
  };
  sortBy: 'priority' | 'created_at' | 'target_date' | 'progress';
  sortOrder: 'asc' | 'desc';
  viewMode: 'list' | 'grid' | 'hierarchy';
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: GoalsState = {
  goals: [],
  currentGoal: null,
  selectedGoals: [],
  goalHierarchy: [],
  filters: {},
  sortBy: 'priority',
  sortOrder: 'desc',
  viewMode: 'list',
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
  },
  isLoading: false,
  error: null,
};

const goalsSlice = createSlice({
  name: 'goals',
  initialState,
  reducers: {
    setGoals: (state, action: PayloadAction<Goal[]>) => {
      state.goals = action.payload;
    },
    
    addGoal: (state, action: PayloadAction<Goal>) => {
      state.goals.unshift(action.payload);
    },
    
    updateGoal: (state, action: PayloadAction<Goal>) => {
      const index = state.goals.findIndex(goal => goal.id === action.payload.id);
      if (index !== -1) {
        state.goals[index] = action.payload;
      }
      
      if (state.currentGoal?.id === action.payload.id) {
        state.currentGoal = action.payload;
      }
    },
    
    removeGoal: (state, action: PayloadAction<string>) => {
      state.goals = state.goals.filter(goal => goal.id !== action.payload);
      state.selectedGoals = state.selectedGoals.filter(id => id !== action.payload);
      
      if (state.currentGoal?.id === action.payload) {
        state.currentGoal = null;
      }
    },
    
    setCurrentGoal: (state, action: PayloadAction<Goal | null>) => {
      state.currentGoal = action.payload;
    },
    
    setSelectedGoals: (state, action: PayloadAction<string[]>) => {
      state.selectedGoals = action.payload;
    },
    
    toggleGoalSelection: (state, action: PayloadAction<string>) => {
      const goalId = action.payload;
      const index = state.selectedGoals.indexOf(goalId);
      
      if (index === -1) {
        state.selectedGoals.push(goalId);
      } else {
        state.selectedGoals.splice(index, 1);
      }
    },
    
    clearGoalSelection: (state) => {
      state.selectedGoals = [];
    },
    
    setGoalHierarchy: (state, action: PayloadAction<Goal[]>) => {
      state.goalHierarchy = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<GoalsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    setSortBy: (state, action: PayloadAction<GoalsState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    
    setSortOrder: (state, action: PayloadAction<GoalsState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
    
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    setViewMode: (state, action: PayloadAction<GoalsState['viewMode']>) => {
      state.viewMode = action.payload;
    },
    
    setPagination: (state, action: PayloadAction<Partial<GoalsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    
    resetPagination: (state) => {
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
      };
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    updateGoalProgress: (state, action: PayloadAction<{ goalId: string; currentValue: number }>) => {
      const { goalId, currentValue } = action.payload;
      const goal = state.goals.find(g => g.id === goalId);
      
      if (goal) {
        goal.currentValue = currentValue;
        
        // Update status if goal is completed
        if (goal.targetValue && currentValue >= goal.targetValue && goal.status !== 'completed') {
          goal.status = 'completed';
          goal.completedAt = new Date().toISOString();
        }
      }
      
      if (state.currentGoal?.id === goalId) {
        state.currentGoal.currentValue = currentValue;
        if (state.currentGoal.targetValue && currentValue >= state.currentGoal.targetValue) {
          state.currentGoal.status = 'completed';
          state.currentGoal.completedAt = new Date().toISOString();
        }
      }
    },
    
    resetGoalsState: (state) => {
      return initialState;
    },
  },
});

export const {
  setGoals,
  addGoal,
  updateGoal,
  removeGoal,
  setCurrentGoal,
  setSelectedGoals,
  toggleGoalSelection,
  clearGoalSelection,
  setGoalHierarchy,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setViewMode,
  setPagination,
  resetPagination,
  setLoading,
  setError,
  clearError,
  updateGoalProgress,
  resetGoalsState,
} = goalsSlice.actions;

export default goalsSlice.reducer;

// Selectors
export const selectGoals = (state: { goals: GoalsState }) => state.goals.goals;
export const selectCurrentGoal = (state: { goals: GoalsState }) => state.goals.currentGoal;
export const selectSelectedGoals = (state: { goals: GoalsState }) => state.goals.selectedGoals;
export const selectGoalHierarchy = (state: { goals: GoalsState }) => state.goals.goalHierarchy;
export const selectGoalFilters = (state: { goals: GoalsState }) => state.goals.filters;
export const selectGoalSortBy = (state: { goals: GoalsState }) => state.goals.sortBy;
export const selectGoalSortOrder = (state: { goals: GoalsState }) => state.goals.sortOrder;
export const selectGoalViewMode = (state: { goals: GoalsState }) => state.goals.viewMode;
export const selectGoalPagination = (state: { goals: GoalsState }) => state.goals.pagination;
export const selectGoalsLoading = (state: { goals: GoalsState }) => state.goals.isLoading;
export const selectGoalsError = (state: { goals: GoalsState }) => state.goals.error;
