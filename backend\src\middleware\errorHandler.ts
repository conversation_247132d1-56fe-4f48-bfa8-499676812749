import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  code?: string;
}

/**
 * Custom error class for application errors
 */
export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Predefined error types
 */
export class ValidationError extends CustomError {
  constructor(message: string = 'Validation failed') {
    super(message, 400, true, 'VALIDATION_ERROR');
  }
}

export class AuthenticationError extends CustomError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends CustomError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true, 'CONFLICT_ERROR');
  }
}

export class RateLimitError extends CustomError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
  }
}

export class DatabaseError extends CustomError {
  constructor(message: string = 'Database operation failed') {
    super(message, 500, true, 'DATABASE_ERROR');
  }
}

export class ExternalServiceError extends CustomError {
  constructor(message: string = 'External service unavailable') {
    super(message, 502, true, 'EXTERNAL_SERVICE_ERROR');
  }
}

/**
 * Error response interface
 */
interface ErrorResponse {
  error: {
    message: string;
    code?: string;
    statusCode: number;
    timestamp: string;
    path: string;
    method: string;
    stack?: string;
    details?: any;
  };
}

/**
 * Format error response
 */
const formatErrorResponse = (err: AppError, req: Request): ErrorResponse => {
  const response: ErrorResponse = {
    error: {
      message: err.message || 'Internal Server Error',
      code: err.code,
      statusCode: err.statusCode || 500,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method,
    }
  };

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.error.stack = err.stack;
  }

  return response;
};

/**
 * Log error with context
 */
const logError = (err: AppError, req: Request) => {
  const errorContext = {
    message: err.message,
    statusCode: err.statusCode,
    code: err.code,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    body: req.body,
    params: req.params,
    query: req.query,
  };

  if (err.statusCode && err.statusCode >= 500) {
    logger.error('Server Error:', errorContext);
  } else if (err.statusCode && err.statusCode >= 400) {
    logger.warn('Client Error:', errorContext);
  } else {
    logger.error('Unknown Error:', errorContext);
  }
};

/**
 * Handle different types of errors
 */
const handleSpecificErrors = (err: any): AppError => {
  // PostgreSQL errors
  if (err.code === '23505') {
    return new ConflictError('Resource already exists');
  }
  
  if (err.code === '23503') {
    return new ValidationError('Referenced resource does not exist');
  }
  
  if (err.code === '23502') {
    return new ValidationError('Required field is missing');
  }

  // Neo4j errors
  if (err.code && err.code.startsWith('Neo.')) {
    return new DatabaseError('Graph database operation failed');
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return new AuthenticationError('Invalid token');
  }
  
  if (err.name === 'TokenExpiredError') {
    return new AuthenticationError('Token expired');
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    return new ValidationError(err.message);
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    return new ValidationError('File size too large');
  }
  
  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return new ValidationError('Unexpected file field');
  }

  // Redis errors
  if (err.code === 'ECONNREFUSED' && err.port === 6379) {
    return new ExternalServiceError('Cache service unavailable');
  }

  // Default to the original error if it's already an AppError
  if (err.statusCode && err.isOperational !== undefined) {
    return err as AppError;
  }

  // Default server error
  return new CustomError('Internal Server Error', 500, false);
};

/**
 * Main error handling middleware
 */
export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Handle the error
  const handledError = handleSpecificErrors(err);
  
  // Log the error
  logError(handledError, req);
  
  // Format and send response
  const errorResponse = formatErrorResponse(handledError, req);
  
  res.status(handledError.statusCode || 500).json(errorResponse);
};

/**
 * Async error wrapper
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 handler
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Validation error handler
 */
export const handleValidationError = (errors: any[]) => {
  const message = errors.map(error => error.msg).join(', ');
  throw new ValidationError(message);
};
