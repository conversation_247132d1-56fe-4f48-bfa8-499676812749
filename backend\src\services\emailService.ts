import { google } from 'googleapis';
import { logger } from '@/utils/logger';
import { CustomError } from '@/middleware/errorHandler';
import { nlpService } from './nlpService';

export interface EmailProcessingResult {
  processedContent: string;
  extractedTasks: EmailTask[];
  extractedEvents: EmailEvent[];
  extractedContacts: EmailContact[];
  metadata: {
    subject: string;
    sender: string;
    recipient: string;
    date: string;
    importance: 'low' | 'medium' | 'high';
    category: string;
    sentiment: string;
  };
}

export interface EmailTask {
  title: string;
  description: string;
  dueDate?: string;
  priority: 'low' | 'medium' | 'high';
  assignee?: string;
  context: string;
}

export interface EmailEvent {
  title: string;
  description: string;
  startDate: string;
  endDate?: string;
  location?: string;
  attendees: string[];
  context: string;
}

export interface EmailContact {
  name: string;
  email: string;
  role?: string;
  organization?: string;
  context: string;
}

class EmailService {
  private gmail: any;

  constructor() {
    this.initializeGmailAPI();
  }

  /**
   * Initialize Gmail API
   */
  private initializeGmailAPI(): void {
    try {
      if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
        const oauth2Client = new google.auth.OAuth2(
          process.env.GOOGLE_CLIENT_ID,
          process.env.GOOGLE_CLIENT_SECRET,
          'http://localhost:3000/auth/google/callback'
        );

        this.gmail = google.gmail({ version: 'v1', auth: oauth2Client });
      }
    } catch (error) {
      logger.error('Error initializing Gmail API:', error);
    }
  }

  /**
   * Process email content and extract actionable items
   */
  async processEmail(emailContent: string, metadata?: {
    subject?: string;
    sender?: string;
    recipient?: string;
    date?: string;
  }): Promise<EmailProcessingResult> {
    try {
      logger.info('Processing email content for actionable items');

      // Clean and normalize email content
      const cleanContent = this.cleanEmailContent(emailContent);

      // Analyze content with NLP
      const nlpResult = await nlpService.analyzeText(cleanContent);

      // Extract tasks from email
      const extractedTasks = this.extractTasksFromEmail(cleanContent, nlpResult);

      // Extract events/meetings from email
      const extractedEvents = this.extractEventsFromEmail(cleanContent, nlpResult);

      // Extract contacts from email
      const extractedContacts = this.extractContactsFromEmail(cleanContent, nlpResult);

      // Determine email importance and category
      const importance = this.determineEmailImportance(cleanContent, nlpResult, metadata);
      const category = this.categorizeEmail(cleanContent, nlpResult);

      const result: EmailProcessingResult = {
        processedContent: cleanContent,
        extractedTasks,
        extractedEvents,
        extractedContacts,
        metadata: {
          subject: metadata?.subject || 'No Subject',
          sender: metadata?.sender || 'Unknown Sender',
          recipient: metadata?.recipient || 'Unknown Recipient',
          date: metadata?.date || new Date().toISOString(),
          importance,
          category,
          sentiment: nlpResult.sentiment.label
        }
      };

      logger.info(`Email processed: ${extractedTasks.length} tasks, ${extractedEvents.length} events, ${extractedContacts.length} contacts`);
      return result;

    } catch (error) {
      logger.error('Error processing email:', error);
      throw new CustomError('Failed to process email content');
    }
  }

  /**
   * Clean email content by removing signatures, headers, etc.
   */
  private cleanEmailContent(content: string): string {
    // Remove email headers
    let cleaned = content.replace(/^(From|To|Cc|Bcc|Subject|Date):.*$/gm, '');

    // Remove email signatures (common patterns)
    cleaned = cleaned.replace(/--\s*\n[\s\S]*$/m, '');
    cleaned = cleaned.replace(/Best regards[\s\S]*$/im, '');
    cleaned = cleaned.replace(/Sincerely[\s\S]*$/im, '');
    cleaned = cleaned.replace(/Thanks[\s\S]*$/im, '');

    // Remove quoted text (replies)
    cleaned = cleaned.replace(/^>.*$/gm, '');
    cleaned = cleaned.replace(/On .* wrote:[\s\S]*$/m, '');

    // Remove excessive whitespace
    cleaned = cleaned.replace(/\n\s*\n/g, '\n\n');
    cleaned = cleaned.replace(/^\s+|\s+$/g, '');

    return cleaned;
  }

  /**
   * Extract tasks from email content
   */
  private extractTasksFromEmail(content: string, nlpResult: any): EmailTask[] {
    const tasks: EmailTask[] = [];

    // Use NLP action items as base
    if (nlpResult.actionItems) {
      nlpResult.actionItems.forEach((item: any) => {
        tasks.push({
          title: item.text,
          description: this.getTaskContext(content, item.text),
          priority: item.urgency || 'medium',
          context: 'email'
        });
      });
    }

    // Additional task extraction patterns
    const taskPatterns = [
      /please\s+(.*?)(?:\.|$)/gi,
      /could you\s+(.*?)(?:\.|$)/gi,
      /can you\s+(.*?)(?:\.|$)/gi,
      /need you to\s+(.*?)(?:\.|$)/gi,
      /action required:\s*(.*?)(?:\.|$)/gi,
      /todo:\s*(.*?)(?:\.|$)/gi,
      /task:\s*(.*?)(?:\.|$)/gi
    ];

    taskPatterns.forEach(pattern => {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && match[1].trim().length > 5) {
          tasks.push({
            title: match[1].trim(),
            description: this.getTaskContext(content, match[1]),
            priority: this.determinePriority(match[0]),
            context: 'email'
          });
        }
      }
    });

    // Extract due dates
    tasks.forEach(task => {
      const dueDate = this.extractDueDate(content, task.title);
      if (dueDate) {
        task.dueDate = dueDate;
      }
    });

    return tasks.slice(0, 10); // Limit to 10 tasks
  }

  /**
   * Extract events/meetings from email content
   */
  private extractEventsFromEmail(content: string, nlpResult: any): EmailEvent[] {
    const events: EmailEvent[] = [];

    // Meeting patterns
    const meetingPatterns = [
      /meeting\s+(?:on\s+)?([^.]+)/gi,
      /call\s+(?:on\s+)?([^.]+)/gi,
      /conference\s+(?:on\s+)?([^.]+)/gi,
      /appointment\s+(?:on\s+)?([^.]+)/gi,
      /interview\s+(?:on\s+)?([^.]+)/gi
    ];

    meetingPatterns.forEach(pattern => {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const eventText = match[1].trim();
        const dateTime = this.extractDateTime(eventText);
        const location = this.extractLocation(eventText);
        const attendees = this.extractAttendees(content);

        if (dateTime) {
          events.push({
            title: match[0].split(/\s+(?:on\s+)?/)[0],
            description: eventText,
            startDate: dateTime,
            location,
            attendees,
            context: 'email'
          });
        }
      }
    });

    return events.slice(0, 5); // Limit to 5 events
  }

  /**
   * Extract contacts from email content
   */
  private extractContactsFromEmail(content: string, nlpResult: any): EmailContact[] {
    const contacts: EmailContact[] = [];

    // Extract email addresses
    const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    const emails = content.match(emailPattern) || [];

    emails.forEach(email => {
      const name = this.extractNameForEmail(content, email);
      const organization = this.extractOrganization(content, email);

      contacts.push({
        name: name || email.split('@')[0],
        email,
        organization,
        context: 'email'
      });
    });

    // Extract names from entities
    if (nlpResult.entities) {
      nlpResult.entities.forEach((entity: any) => {
        if (entity.type === 'PERSON' && entity.confidence > 0.8) {
          const existingContact = contacts.find(c => 
            c.name.toLowerCase().includes(entity.name.toLowerCase())
          );

          if (!existingContact) {
            contacts.push({
              name: entity.name,
              email: '',
              context: 'email'
            });
          }
        }
      });
    }

    return contacts.slice(0, 10); // Limit to 10 contacts
  }

  /**
   * Determine email importance
   */
  private determineEmailImportance(
    content: string, 
    nlpResult: any, 
    metadata?: any
  ): 'low' | 'medium' | 'high' {
    let score = 0;

    // Check for urgency keywords
    const urgentKeywords = ['urgent', 'asap', 'immediately', 'critical', 'emergency'];
    const importantKeywords = ['important', 'priority', 'deadline', 'action required'];

    urgentKeywords.forEach(keyword => {
      if (content.toLowerCase().includes(keyword)) {
        score += 3;
      }
    });

    importantKeywords.forEach(keyword => {
      if (content.toLowerCase().includes(keyword)) {
        score += 2;
      }
    });

    // Check subject line
    if (metadata?.subject) {
      if (metadata.subject.includes('!') || metadata.subject.toUpperCase() === metadata.subject) {
        score += 2;
      }
    }

    // Check for action items
    if (nlpResult.actionItems && nlpResult.actionItems.length > 0) {
      score += nlpResult.actionItems.length;
    }

    if (score >= 5) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
  }

  /**
   * Categorize email content
   */
  private categorizeEmail(content: string, nlpResult: any): string {
    const categories = {
      'meeting': ['meeting', 'call', 'conference', 'appointment'],
      'project': ['project', 'deliverable', 'milestone', 'deadline'],
      'task': ['task', 'todo', 'action', 'complete'],
      'information': ['fyi', 'information', 'update', 'status'],
      'request': ['request', 'please', 'could you', 'can you'],
      'social': ['lunch', 'coffee', 'social', 'team building']
    };

    const contentLower = content.toLowerCase();
    let maxScore = 0;
    let category = 'general';

    Object.entries(categories).forEach(([cat, keywords]) => {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (contentLower.split(keyword).length - 1);
      }, 0);

      if (score > maxScore) {
        maxScore = score;
        category = cat;
      }
    });

    return category;
  }

  /**
   * Helper methods for extraction
   */
  private getTaskContext(content: string, taskText: string): string {
    const sentences = content.split(/[.!?]+/);
    const taskSentence = sentences.find(s => s.includes(taskText));
    return taskSentence ? taskSentence.trim() : taskText;
  }

  private determinePriority(text: string): 'low' | 'medium' | 'high' {
    const urgentWords = ['urgent', 'asap', 'immediately', 'critical'];
    const importantWords = ['important', 'priority', 'soon'];

    const textLower = text.toLowerCase();
    
    if (urgentWords.some(word => textLower.includes(word))) {
      return 'high';
    }
    
    if (importantWords.some(word => textLower.includes(word))) {
      return 'medium';
    }
    
    return 'low';
  }

  private extractDueDate(content: string, taskText: string): string | undefined {
    const datePatterns = [
      /by\s+(\w+\s+\d{1,2}(?:st|nd|rd|th)?)/gi,
      /due\s+(\w+\s+\d{1,2}(?:st|nd|rd|th)?)/gi,
      /deadline\s+(\w+\s+\d{1,2}(?:st|nd|rd|th)?)/gi
    ];

    for (const pattern of datePatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return undefined;
  }

  private extractDateTime(text: string): string | undefined {
    // Simple date/time extraction - in production, use a proper date parsing library
    const dateTimePattern = /(\w+\s+\d{1,2}(?:st|nd|rd|th)?\s+at\s+\d{1,2}:\d{2})/gi;
    const match = text.match(dateTimePattern);
    return match ? match[0] : undefined;
  }

  private extractLocation(text: string): string | undefined {
    const locationPattern = /(?:at|in|location:)\s+([^,.\n]+)/gi;
    const match = text.match(locationPattern);
    return match ? match[0].replace(/^(?:at|in|location:)\s+/i, '') : undefined;
  }

  private extractAttendees(content: string): string[] {
    const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    return content.match(emailPattern) || [];
  }

  private extractNameForEmail(content: string, email: string): string | undefined {
    // Look for name patterns near the email
    const namePattern = new RegExp(`([A-Z][a-z]+\\s+[A-Z][a-z]+)\\s*<?${email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}>?`, 'i');
    const match = content.match(namePattern);
    return match ? match[1] : undefined;
  }

  private extractOrganization(content: string, email: string): string | undefined {
    const domain = email.split('@')[1];
    const orgName = domain.split('.')[0];
    return orgName.charAt(0).toUpperCase() + orgName.slice(1);
  }

  /**
   * Fetch emails from Gmail (requires OAuth setup)
   */
  async fetchEmails(accessToken: string, maxResults: number = 10): Promise<any[]> {
    try {
      if (!this.gmail) {
        throw new CustomError('Gmail API not configured');
      }

      // Set access token
      this.gmail.auth.setCredentials({ access_token: accessToken });

      const response = await this.gmail.users.messages.list({
        userId: 'me',
        maxResults,
        q: 'is:unread'
      });

      const messages = response.data.messages || [];
      const emailDetails = [];

      for (const message of messages) {
        const detail = await this.gmail.users.messages.get({
          userId: 'me',
          id: message.id
        });

        emailDetails.push(detail.data);
      }

      return emailDetails;

    } catch (error) {
      logger.error('Error fetching emails:', error);
      throw new CustomError('Failed to fetch emails from Gmail');
    }
  }
}

export const emailService = new EmailService();
