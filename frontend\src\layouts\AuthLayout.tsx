import React from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { PsychologyRounded } from '@mui/icons-material';

interface AuthLayoutProps {
  children: React.ReactNode;
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
        p: 2,
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* <PERSON><PERSON> and Brand */}
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            mb={4}
          >
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                mb: 2,
              }}
            >
              <PsychologyRounded
                sx={{
                  fontSize: 40,
                  color: 'white',
                }}
              />
            </Box>
            
            <Typography
              variant="h4"
              component="h1"
              fontWeight={700}
              color="primary"
              gutterBottom
            >
              MindSync
            </Typography>
            
            <Typography
              variant="subtitle1"
              color="text.secondary"
              textAlign="center"
            >
              Your second brain for productivity
            </Typography>
          </Box>

          {/* Auth Form Content */}
          {children}

          {/* Footer */}
          <Box mt={4} textAlign="center">
            <Typography variant="caption" color="text.secondary">
              © 2024 MindSync. All rights reserved.
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};
