import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Alert,
} from '@mui/material';

import { useAppDispatch } from '@/store/hooks';
import { setBreadcrumbs } from '@/store/slices/uiSlice';
import { QuickCaptureWidget } from '@/components/dashboard/QuickCaptureWidget';

export const CapturePage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Quick Capture' },
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Universal Capture Hub
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Capture and process any type of content with AI-powered analysis.
      </Typography>

      <Box maxWidth="800px">
        <QuickCaptureWidget />
        
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Coming Soon:</strong> Voice recording, image OCR, email processing, and web clipping features are currently in development.
          </Typography>
        </Alert>
      </Box>
    </Box>
  );
};
