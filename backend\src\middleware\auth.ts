import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { executeQuery } from '@/config/database';
import { logger } from '@/utils/logger';
import { AuthenticationError, AuthorizationError, ValidationError } from './errorHandler';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    firstName?: string;
    lastName?: string;
  };
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

class AuthService {
  private readonly jwtSecret: string;
  private readonly jwtRefreshSecret: string;
  private readonly accessTokenExpiry: string;
  private readonly refreshTokenExpiry: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'fallback-secret-change-in-production';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
    this.accessTokenExpiry = process.env.JWT_EXPIRES_IN || '15m';
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

    if (process.env.NODE_ENV === 'production' && this.jwtSecret.includes('fallback')) {
      logger.error('JWT_SECRET not properly configured for production');
      throw new Error('JWT_SECRET must be set in production');
    }
  }

  /**
   * Generate JWT token pair (access + refresh)
   */
  generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>): TokenPair {
    const accessToken = jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.accessTokenExpiry,
      issuer: 'mindsync-api',
      audience: 'mindsync-client'
    });

    const refreshToken = jwt.sign(
      { userId: payload.userId, type: 'refresh' },
      this.jwtRefreshSecret,
      {
        expiresIn: this.refreshTokenExpiry,
        issuer: 'mindsync-api',
        audience: 'mindsync-client'
      }
    );

    return { accessToken, refreshToken };
  }

  /**
   * Verify JWT access token
   */
  verifyAccessToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'mindsync-api',
        audience: 'mindsync-client'
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Access token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid access token');
      }
      throw new AuthenticationError('Token verification failed');
    }
  }

  /**
   * Verify JWT refresh token
   */
  verifyRefreshToken(token: string): { userId: string; type: string } {
    try {
      const decoded = jwt.verify(token, this.jwtRefreshSecret, {
        issuer: 'mindsync-api',
        audience: 'mindsync-client'
      }) as any;

      if (decoded.type !== 'refresh') {
        throw new AuthenticationError('Invalid refresh token type');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid refresh token');
      }
      throw new AuthenticationError('Refresh token verification failed');
    }
  }

  /**
   * Hash password
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify password
   */
  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<any> {
    const query = `
      SELECT id, email, first_name, last_name, role, is_verified, avatar_url, 
             timezone, preferences, created_at, last_login_at
      FROM users 
      WHERE id = $1
    `;

    const result = await executeQuery(query, [userId]);
    return result.rows[0] || null;
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<any> {
    const query = `
      SELECT id, email, password_hash, first_name, last_name, role, is_verified, 
             avatar_url, timezone, preferences, created_at, last_login_at
      FROM users 
      WHERE email = $1
    `;

    const result = await executeQuery(query, [email.toLowerCase()]);
    return result.rows[0] || null;
  }

  /**
   * Create new user
   */
  async createUser(userData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }): Promise<any> {
    const hashedPassword = await this.hashPassword(userData.password);

    const query = `
      INSERT INTO users (email, password_hash, first_name, last_name, is_verified)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, email, first_name, last_name, role, is_verified, created_at
    `;

    const result = await executeQuery(query, [
      userData.email.toLowerCase(),
      hashedPassword,
      userData.firstName || null,
      userData.lastName || null,
      false // Email verification required
    ]);

    return result.rows[0];
  }

  /**
   * Update user last login
   */
  async updateLastLogin(userId: string): Promise<void> {
    const query = `
      UPDATE users 
      SET last_login_at = NOW() 
      WHERE id = $1
    `;

    await executeQuery(query, [userId]);
  }

  /**
   * Store refresh token (for token rotation)
   */
  async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    // In production, you might want to store refresh tokens in database
    // For now, we'll rely on JWT expiration
    logger.info(`Refresh token generated for user: ${userId}`);
  }

  /**
   * Revoke refresh token
   */
  async revokeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    // Implementation for token revocation
    logger.info(`Refresh token revoked for user: ${userId}`);
  }
}

export const authService = new AuthService();

/**
 * Authentication middleware
 */
export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      throw new AuthenticationError('Authorization header missing');
    }

    const token = authHeader.split(' ')[1]; // Bearer <token>

    if (!token) {
      throw new AuthenticationError('Access token missing');
    }

    // Verify token
    const decoded = authService.verifyAccessToken(token);

    // Get user details
    const user = await authService.getUserById(decoded.userId);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.is_verified) {
      throw new AuthenticationError('Email not verified');
    }

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      firstName: user.first_name,
      lastName: user.last_name
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication middleware (doesn't throw if no token)
 */
export const optionalAuthMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (authHeader) {
      const token = authHeader.split(' ')[1];
      
      if (token) {
        const decoded = authService.verifyAccessToken(token);
        const user = await authService.getUserById(decoded.userId);
        
        if (user && user.is_verified) {
          req.user = {
            id: user.id,
            email: user.email,
            role: user.role,
            firstName: user.first_name,
            lastName: user.last_name
          };
        }
      }
    }

    next();
  } catch (error) {
    // Don't throw error for optional auth
    next();
  }
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (roles: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(req.user.role)) {
      throw new AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
    }

    next();
  };
};

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole('admin');

/**
 * Premium user middleware
 */
export const requirePremium = requireRole(['admin', 'premium']);

/**
 * Validate password strength
 */
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
