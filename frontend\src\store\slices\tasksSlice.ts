import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Task } from '../api/tasksApi';

export interface TasksState {
  tasks: Task[];
  currentTask: Task | null;
  selectedTasks: string[];
  recommendedTasks: Task[];
  filters: {
    status?: 'todo' | 'in_progress' | 'completed' | 'cancelled';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    goalId?: string;
    search?: string;
    tags?: string[];
    dueDate?: {
      start?: string;
      end?: string;
    };
  };
  sortBy: 'created_at' | 'due_date' | 'priority' | 'complexity_score';
  sortOrder: 'asc' | 'desc';
  viewMode: 'list' | 'grid' | 'kanban';
  groupBy: 'none' | 'status' | 'priority' | 'goal';
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: TasksState = {
  tasks: [],
  currentTask: null,
  selectedTasks: [],
  recommendedTasks: [],
  filters: {},
  sortBy: 'created_at',
  sortOrder: 'desc',
  viewMode: 'list',
  groupBy: 'none',
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
  },
  isLoading: false,
  error: null,
};

const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    setTasks: (state, action: PayloadAction<Task[]>) => {
      state.tasks = action.payload;
    },
    
    addTask: (state, action: PayloadAction<Task>) => {
      state.tasks.unshift(action.payload);
    },
    
    updateTask: (state, action: PayloadAction<Task>) => {
      const index = state.tasks.findIndex(task => task.id === action.payload.id);
      if (index !== -1) {
        state.tasks[index] = action.payload;
      }
      
      if (state.currentTask?.id === action.payload.id) {
        state.currentTask = action.payload;
      }
    },
    
    removeTask: (state, action: PayloadAction<string>) => {
      state.tasks = state.tasks.filter(task => task.id !== action.payload);
      state.selectedTasks = state.selectedTasks.filter(id => id !== action.payload);
      
      if (state.currentTask?.id === action.payload) {
        state.currentTask = null;
      }
    },
    
    setCurrentTask: (state, action: PayloadAction<Task | null>) => {
      state.currentTask = action.payload;
    },
    
    setSelectedTasks: (state, action: PayloadAction<string[]>) => {
      state.selectedTasks = action.payload;
    },
    
    toggleTaskSelection: (state, action: PayloadAction<string>) => {
      const taskId = action.payload;
      const index = state.selectedTasks.indexOf(taskId);
      
      if (index === -1) {
        state.selectedTasks.push(taskId);
      } else {
        state.selectedTasks.splice(index, 1);
      }
    },
    
    clearTaskSelection: (state) => {
      state.selectedTasks = [];
    },
    
    setRecommendedTasks: (state, action: PayloadAction<Task[]>) => {
      state.recommendedTasks = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<TasksState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    setSortBy: (state, action: PayloadAction<TasksState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    
    setSortOrder: (state, action: PayloadAction<TasksState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
    
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    setViewMode: (state, action: PayloadAction<TasksState['viewMode']>) => {
      state.viewMode = action.payload;
    },
    
    setGroupBy: (state, action: PayloadAction<TasksState['groupBy']>) => {
      state.groupBy = action.payload;
    },
    
    setPagination: (state, action: PayloadAction<Partial<TasksState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    
    resetPagination: (state) => {
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
      };
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    updateTaskStatus: (state, action: PayloadAction<{ taskId: string; status: Task['status'] }>) => {
      const { taskId, status } = action.payload;
      const task = state.tasks.find(t => t.id === taskId);
      
      if (task) {
        task.status = status;
        
        if (status === 'completed') {
          task.completedAt = new Date().toISOString();
        } else if (task.completedAt) {
          task.completedAt = undefined;
        }
      }
      
      if (state.currentTask?.id === taskId) {
        state.currentTask.status = status;
        if (status === 'completed') {
          state.currentTask.completedAt = new Date().toISOString();
        } else if (state.currentTask.completedAt) {
          state.currentTask.completedAt = undefined;
        }
      }
    },
    
    bulkUpdateTasks: (state, action: PayloadAction<{ taskIds: string[]; updates: Partial<Task> }>) => {
      const { taskIds, updates } = action.payload;
      
      state.tasks.forEach(task => {
        if (taskIds.includes(task.id)) {
          Object.assign(task, updates);
          
          if (updates.status === 'completed' && !task.completedAt) {
            task.completedAt = new Date().toISOString();
          } else if (updates.status !== 'completed' && task.completedAt) {
            task.completedAt = undefined;
          }
        }
      });
    },
    
    reorderTasks: (state, action: PayloadAction<{ sourceIndex: number; destinationIndex: number }>) => {
      const { sourceIndex, destinationIndex } = action.payload;
      const [removed] = state.tasks.splice(sourceIndex, 1);
      state.tasks.splice(destinationIndex, 0, removed);
    },
    
    resetTasksState: (state) => {
      return initialState;
    },
  },
});

export const {
  setTasks,
  addTask,
  updateTask,
  removeTask,
  setCurrentTask,
  setSelectedTasks,
  toggleTaskSelection,
  clearTaskSelection,
  setRecommendedTasks,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setViewMode,
  setGroupBy,
  setPagination,
  resetPagination,
  setLoading,
  setError,
  clearError,
  updateTaskStatus,
  bulkUpdateTasks,
  reorderTasks,
  resetTasksState,
} = tasksSlice.actions;

export default tasksSlice.reducer;

// Selectors
export const selectTasks = (state: { tasks: TasksState }) => state.tasks.tasks;
export const selectCurrentTask = (state: { tasks: TasksState }) => state.tasks.currentTask;
export const selectSelectedTasks = (state: { tasks: TasksState }) => state.tasks.selectedTasks;
export const selectRecommendedTasks = (state: { tasks: TasksState }) => state.tasks.recommendedTasks;
export const selectTaskFilters = (state: { tasks: TasksState }) => state.tasks.filters;
export const selectTaskSortBy = (state: { tasks: TasksState }) => state.tasks.sortBy;
export const selectTaskSortOrder = (state: { tasks: TasksState }) => state.tasks.sortOrder;
export const selectTaskViewMode = (state: { tasks: TasksState }) => state.tasks.viewMode;
export const selectTaskGroupBy = (state: { tasks: TasksState }) => state.tasks.groupBy;
export const selectTaskPagination = (state: { tasks: TasksState }) => state.tasks.pagination;
export const selectTasksLoading = (state: { tasks: TasksState }) => state.tasks.isLoading;
export const selectTasksError = (state: { tasks: TasksState }) => state.tasks.error;
